#!/bin/bash

pre-commit install
export LOCALSTACK_AUTH_TOKEN="ls-qumAQILA-qAKI-wECe-8499-wAbovObU595b"
OS=$(uname -s)
# Check if awslocal is installed
if ! command -v awslocal &>/dev/null; then
  echo "Error: 'awslocal' is not installed. Please install it and try again."
  read -p "Do you want to install it? (y/n): " response
  if [[ "$response" =~ ^[Yy]$ ]]; then

    if [[ "$OS" == "Darwin" ]]; then
      echo "Installing awscli-local..."
      brew install awscli-local
    else
      echo "Installing awscli-local..."
      pip install awscli-local
    fi
  fi
  exit 1
fi
if ! docker compose -f docker-compose.local.yml ps | grep -q 'localstack'; then
  echo "Starting Docker Compose..."
  docker compose -f docker-compose.local.yml up -d
else
  echo "Docker is already running."
fi

echo "Waiting for LocalStack to be ready..."
until docker logs localstack-main | grep -q "Ready."; do
  sleep 2
  echo "Waiting for LocalStack..."
done
echo "LocalStack is ready."

function table_exists() {
  table_name=$1
  if awslocal dynamodb list-tables | grep -q "$table_name"; then
    return 0
  else
    return 1
  fi
}
if table_exists "article"; then
  echo "The table 'article' already exists. Skipping creation."
else
  echo "Creating table 'article'..."
  awslocal dynamodb create-table \
    --table-name article --key-schema AttributeName=id,KeyType=HASH \
    --attribute-definitions AttributeName=id,AttributeType=S \
    --billing-mode PAY_PER_REQUEST
fi

if table_exists "contactUs"; then
  echo "The table 'contactUs' already exists. Skipping creation."
else
  echo "Creating table 'contactUs'..."
  awslocal dynamodb create-table \
    --table-name contactUs --key-schema AttributeName=id,KeyType=HASH \
    --attribute-definitions AttributeName=id,AttributeType=S \
    --billing-mode PAY_PER_REQUEST
fi

if table_exists "team"; then
  echo "The table 'team' already exists. Skipping creation."
else
  echo "Creating table 'team'..."
  awslocal dynamodb create-table \
    --table-name team \
    --key-schema AttributeName=id,KeyType=HASH \
    --attribute-definitions AttributeName=id,AttributeType=S \
    --billing-mode PAY_PER_REQUEST

  echo "Inserting first into 'team' table..."
  awslocal dynamodb put-item \
    --table-name team \
    --item '{
     "id": {"S": "0"},
      "name": {"S": "Default Team"},
      "invitationLink": {"S": "http://localhost:3000/invite/608864df28"},
      "invitationCode": {"S": "608864df28"},
      "isRestrictedByDomains": {"BOOL": false},
      "restrictedByTheDomains": {"L": []}
    }'
fi

if table_exists "user"; then
  echo "The table 'user' already exists. Skipping creation."
else
  echo "Creating table 'user'..."
  awslocal dynamodb create-table \
    --table-name user \
    --key-schema AttributeName=id,KeyType=HASH \
    --attribute-definitions AttributeName=id,AttributeType=S \
    --billing-mode PAY_PER_REQUEST

  echo "Inserting admin user into 'user' table..."
  awslocal dynamodb put-item \
    --table-name user \
    --item '{
      "id": {"S": "0"},
			"teamId": {"S": "0"},
      "firstName": {"S": "Admin User"},
      "lastName": {"S": "Admin User"},
      "email": {"S": "<EMAIL>"},
      "role": {"S": "ADMIN"},
      "phoneNumber": {"N": "1234567890"},
      "password": {"S": "8c6976e5b5410415bde908bd4dee15dfb167a9c873fc4bb8a81f6f2ab448a918"},
			"teamInvitationState": {"S": "ACCEPTED"}
    }'
fi

if table_exists "auth-js"; then
  echo "The table 'auth-js' already exists. Skipping creation."
else
  awslocal dynamodb create-table \
    --table-name auth-js \
    --billing-mode PAY_PER_REQUEST \
    --key-schema AttributeName=pk,KeyType=HASH AttributeName=sk,KeyType=RANGE \
    --attribute-definitions \
    AttributeName=pk,AttributeType=S \
    AttributeName=sk,AttributeType=S \
    AttributeName=GSI1PK,AttributeType=S \
    AttributeName=GSI1SK,AttributeType=S \
    --global-secondary-indexes '[{
        "IndexName": "GSI1",
        "KeySchema": [
          {"AttributeName": "GSI1PK", "KeyType": "HASH"},
          {"AttributeName": "GSI1SK", "KeyType": "RANGE"}
        ],
        "Projection": {"ProjectionType": "ALL"}
      }]'
fi
if table_exists "comments"; then
  echo "The table 'comments' already exists. Skipping creation."
else
  echo "Creating table 'comments'..."
  awslocal dynamodb create-table \
    --table-name comments --key-schema AttributeName=id,KeyType=HASH \
    --attribute-definitions AttributeName=id,AttributeType=S \
    --billing-mode PAY_PER_REQUEST
fi
if table_exists "invitation-codes"; then
  echo "the table 'comments' already exists. skipping creation."
else
  echo "creating table 'invitation-codes'..."
  awslocal dynamodb create-table \
    --table-name invitation-codes --key-schema attributename=id,keytype=hash \
    --attribute-definitions attributename=id,attributetype=s \
    --billing-mode pay_per_request
fi
####

if table_exists "AuthUser"; then
  echo "The table 'AuthUser' already exists. Skipping creation."
else
  echo "Creating table 'AuthUser'..."
  awslocal dynamodb create-table \
    --table-name AuthUser \
    --key-schema \
    AttributeName=user_id,KeyType=HASH \
    AttributeName=tenant_id,KeyType=RANGE \
    --attribute-definitions \
    AttributeName=user_id,AttributeType=S \
    AttributeName=tenant_id,AttributeType=S \
    AttributeName=email,AttributeType=S \
    AttributeName=phone,AttributeType=S \
    --billing-mode PAY_PER_REQUEST \
    --global-secondary-indexes '[
      {
        "IndexName": "email-index",
        "KeySchema": [{"AttributeName": "email", "KeyType": "HASH"}],
        "Projection": {"ProjectionType": "ALL"}
      },

      {
        "IndexName": "phone-index",
        "KeySchema": [{"AttributeName": "phone", "KeyType": "HASH"}],
        "Projection": {"ProjectionType": "ALL"}
      }
    ]'
fi

if table_exists "AuthGroup"; then
  echo "The table 'AuthGroup' already exists. Skipping creation."
else
  echo "Creating table 'AuthGroup'..."
  awslocal dynamodb create-table \
    --table-name AuthGroup \
    --key-schema \
    AttributeName=group_id,KeyType=HASH \
    --attribute-definitions \
    AttributeName=group_id,AttributeType=S \
    AttributeName=editor_id,AttributeType=S \
    AttributeName=tenant_id,AttributeType=S \
    AttributeName=subscription_id,AttributeType=S \
    AttributeName=subscription_status,AttributeType=S \
    AttributeName=group_type,AttributeType=S \
    --billing-mode PAY_PER_REQUEST \
    --global-secondary-indexes '[
      {
        "IndexName": "editor_id-index",
        "KeySchema": [{"AttributeName": "editor_id", "KeyType": "HASH"}],
        "Projection": {"ProjectionType": "ALL"}
      },
      {
        "IndexName": "tenant_id-index",
        "KeySchema": [{"AttributeName": "tenant_id", "KeyType": "HASH"}],
        "Projection": {"ProjectionType": "ALL"}
      },
      {
        "IndexName": "subscription_id-index",
        "KeySchema": [{"AttributeName": "subscription_id", "KeyType": "HASH"}],
        "Projection": {"ProjectionType": "ALL"}
      },
      {
        "IndexName": "subscription_status-index",
        "KeySchema": [{"AttributeName": "subscription_status", "KeyType": "HASH"}],
        "Projection": {"ProjectionType": "ALL"}
      },
      {
        "IndexName": "group_type-index",
        "KeySchema": [{"AttributeName": "group_type", "KeyType": "HASH"}],
        "Projection": {"ProjectionType": "ALL"}
      }
    ]'
fi
if table_exists "AuthGroupMembership"; then
  echo "The table 'AuthGroupMembership' already exists. Skipping creation."
else
  echo "Creating table 'AuthGroupMembership'..."
  awslocal dynamodb create-table \
    --table-name AuthGroupMembership \
    --key-schema \
    AttributeName=group_id,KeyType=HASH \
    AttributeName=user_id,KeyType=RANGE \
    --attribute-definitions \
    AttributeName=group_id,AttributeType=S \
    AttributeName=user_id,AttributeType=S \
    AttributeName=tenant_id,AttributeType=S \
    --billing-mode PAY_PER_REQUEST \
    --global-secondary-indexes '[
      {
        "IndexName": "tenant_id-index",
        "KeySchema": [{"AttributeName": "tenant_id", "KeyType": "HASH"}],
        "Projection": {"ProjectionType": "ALL"}
      }
         ]'
fi

if table_exists "AuthSignInTokenLink"; then
  echo "The table 'AuthSignInTokenLink' already exists. Skipping creation."
else
  echo "Creating table 'AuthSignInTokenLink'..."
  awslocal dynamodb create-table \
    --table-name AuthSignInTokenLink \
    --key-schema \
    AttributeName=token,KeyType=HASH \
    AttributeName=tenant_id,KeyType=RANGE \
    --attribute-definitions \
    AttributeName=token,AttributeType=S \
    AttributeName=tenant_id,AttributeType=S \
    --billing-mode PAY_PER_REQUEST
fi

if table_exists "AuthAccessLog"; then
  echo "The table 'AuthAccessLog' already exists. Skipping creation."
else
  echo "Creating table 'AuthAccessLog'..."
  awslocal dynamodb create-table \
    --table-name AuthAccessLog \
    --key-schema \
    AttributeName=attempt_id,KeyType=HASH \
    --attribute-definitions \
    AttributeName=attempt_id,AttributeType=S \
    --billing-mode PAY_PER_REQUEST
fi

if table_exists "AuthRole"; then
  echo "The table 'AuthRole' already exists. Skipping creation."
else
  echo "Creating table 'AuthRole'..."
  awslocal dynamodb create-table \
    --table-name AuthRole \
    --key-schema \
    AttributeName=role_id,KeyType=HASH \
    --attribute-definitions \
    AttributeName=role_id,AttributeType=S \
    --billing-mode PAY_PER_REQUEST
fi

if table_exists "AuthPermission"; then
  echo "The table 'AuthPermission' already exists. Skipping creation."
else
  echo "Creating table 'AuthPermission'..."
  awslocal dynamodb create-table \
    --table-name AuthPermission \
    --key-schema \
    AttributeName=permission_id,KeyType=HASH \
    --attribute-definitions \
    AttributeName=permission_id,AttributeType=S \
    --billing-mode PAY_PER_REQUEST
fi

if table_exists "AuthRolePermission"; then
  echo "The table 'AuthRolePermission' already exists. Skipping creation."
else
  echo "Creating table 'AuthRolePermission'..."
  awslocal dynamodb create-table \
    --table-name AuthRolePermission \
    --key-schema \
    AttributeName=role_id,KeyType=HASH \
    AttributeName=permission_id,KeyType=RANGE \
    --attribute-definitions \
    AttributeName=role_id,AttributeType=S \
    AttributeName=permission_id,AttributeType=S \
    --billing-mode PAY_PER_REQUEST
fi

if table_exists "AuthVerificationToken"; then
  echo "The table 'AuthVerificationToken' already exists. Skipping creation."
else
  echo "Creating table 'AuthVerificationToken'..."
  awslocal dynamodb create-table \
    --table-name AuthVerificationToken \
    --key-schema \
    AttributeName=id,KeyType=HASH \
    --attribute-definitions \
    AttributeName=id,AttributeType=S \
    --billing-mode PAY_PER_REQUEST
fi

##insert Data
awslocal dynamodb put-item \
  --table-name AuthUser \
  --item '{
    "user_id": {"S": "0196ceb0-84c5-7599-b884-62a68603580b"},
    "tenant_id": {"S": "1"},
    "password": {"S": "$2b$10$EaARv9BevJGX9ytBdW6WPuwVl.SwH6D7utHol9VBn2fQ9ZRQMQ0Ge"},
    "social_provider": {"S": ""},
    "phoneNumber": {"S": "890329482"},
    "roles_ids": {"L": [
      {"S": "0196c4b8-51b2-77c2-824e-c7e187b78de4"},
      {"S": "0196cae2-9c3a-7b8b-8897-a7a5d6ddce35"}
    ]},
    "name": {"S": "saul burgos"},
    "failed_attempt_count": {"N": "0"},
    "email": {"S": "<EMAIL>"}
  }'

awslocal dynamodb put-item \
  --table-name AuthUser \
  --item '{
    "user_id": {"S": "0196ceb2-1f0a-793f-81a9-d45e519be644"},
    "tenant_id": {"S": "1"},
    "password": {"S": "$2b$10$dHm4/FKe3VwJqp.bwl3WAeabW6q7/vl2QHU9Y8.QZDZNhAiZMbhNS"},
    "social_provider": {"S": ""},
    "phoneNumber": {"S": "393829493"},
    "roles_ids": {"L": [
      {"S": "0196c4b8-51b2-77c2-824e-c7e187b78de4"},
      {"S": "0196cae2-9c3a-7b8b-8897-a7a5d6ddce35"},
      {"S": "0196cec0-0f3c-734f-8781-5dc33ae18deb"}

    ]},
    "name": {"S": "fhillip castillo"},
    "failed_attempt_count": {"N": "0"},
    "email": {"S": "<EMAIL>"}
  }'

awslocal dynamodb put-item \
  --table-name AuthUser \
  --item '{
    "user_id": {"S": "0196ceb3-123c-702a-8779-684798ad581d"},
    "tenant_id": {"S": "1"},
    "password": {"S": "$2b$10$qO21G3wMoT9FtweCxDW8heF2guYNvyw83wTu2vYMwsL8fvTCIZurO"},
    "social_provider": {"S": ""},
    "phoneNumber": {"S": "829958392"},
    "roles_ids": {"L": [
      {"S": "0196c4b8-51b2-77c2-824e-c7e187b78de4"},
      {"S": "0196cae2-9c3a-7b8b-8897-a7a5d6ddce35"},
      {"S": "0196cec0-0f3c-734f-8781-5dc33ae18deb"}

    ]},
    "name": {"S": "edwin cruz"},
    "failed_attempt_count": {"N": "0"},
    "email": {"S": "<EMAIL>"}
  }'

awslocal dynamodb put-item \
  --table-name AuthUser \
  --item '{
    "user_id": {"S": "0196ceb1-4d98-75ea-ae40-6a05c4985a82"},
    "tenant_id": {"S": "1"},
    "password": {"S": "$2b$10$x33.6Hj3VExzuCQtJ8p02.7KsgRVBjifWMECxRvTdSP5JIEgxI/CK"},
    "social_provider": {"S": ""},
    "phoneNumber": {"S": "**********"},
    "roles_ids": {"L": [
      {"S": "0196c4b8-51b2-77c2-824e-c7e187b78de4"},
      {"S": "0196cae2-9c3a-7b8b-8897-a7a5d6ddce35"},
      {"S": "0196cec0-0f3c-734f-8781-5dc33ae18deb"}
    ]},
    "name": {"S": "yelisson ortiz"},
    "failed_attempt_count": {"N": "0"},
    "email": {"S": "<EMAIL>"}
  }'
##Roles
awslocal dynamodb put-item \
  --table-name AuthRole \
  --item '{
    "role_id": {"S": "0196c4b8-51b2-77c2-824e-c7e187b78de4"},
    "tennat_Id": {"S": "1"},
    "role_name": {"S": "admin"},
    "role_description": {"S": "Administrator with full access"},
    "created_at": {"S": "2025-05-12T13:38:56.050Z"},
    "role_attributes": {
      "M": {
        "max_article_edits_per_day": {"N": "50"},
        "default_clearance_level": {"N": "3"},
        "can_approve_authors": {"BOOL": true}
      }
    }
  }'

awslocal dynamodb put-item \
  --table-name AuthRole \
  --item '{
    "role_id": {"S": "0196cae2-9c3a-7b8b-8897-a7a5d6ddce35"},
    "tennat_Id": {"S": "1"},
    "role_name": {"S": "editor"},
    "role_description": {"S": "Administrator with full access"},
    "created_at": {"S": "2025-05-13T18:22:50.942Z"},
    "role_attributes": {"M": {}}
  }'

awslocal dynamodb put-item \
  --table-name AuthRole \
  --item '{
    "role_id": {"S": "0196cacb-ca77-792e-9d1e-3d61fcd20bab"},
    "tennat_Id": {"S": "1"},
    "role_name": {"S": "guest"},
    "role_description": {"S": "Administrator with full access"},
    "created_at": {"S": "2025-05-13T17:57:55.447Z"},
    "role_attributes": {"M": {}}
  }'

awslocal dynamodb put-item \
  --table-name AuthRole \
  --item '{
    "role_id": {"S": "0196cec0-0f3c-734f-8781-5dc33ae18deb"},
    "tennat_Id": {"S": "1"},
    "role_name": {"S": "author"},
    "role_description": {"S": "Administrator with full access"},
    "created_at": {"S": "2025-05-14T12:23:35.485Z"},
    "role_attributes": {"M": {}}
  }'
####
cd app-clients/uptownmedia-nyc
npm i --no-workspaces
gen-dotenv generate-env local
npm run dev
