import type { User, UserPreferences } from "@/types";

const defaultGetOptions = {
	method: "GET",
	headers: {
		"Content-Type": "application/json",
	},
};

const baseApiUrl = "/api";

export const getUserPreferences = async () => {
	// return fetchJsonResponse(`${baseApiUrl}/user/preferences`, defaultGetOptions);
	return Promise.resolve<UserPreferences>({
		theme: "dark",
		sidebar: {
			compact: false,
		},
	});
};

export const updateUserPreferences = async (preferences: UserPreferences) => {
	// return fetchJsonResponse(`${baseApiUrl}/user/preferences`, {
	//   ...defaultGetOptions,
	//   method: "PUT",
	//   body: JSON.stringify(preferences),
	// });
	return Promise.resolve<UserPreferences>(preferences);
};
