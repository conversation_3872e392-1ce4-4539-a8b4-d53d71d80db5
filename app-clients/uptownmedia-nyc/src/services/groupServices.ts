import { fetchClient } from "@/utils/fetchClient";
import type { Group } from "../app/api/groups/dao/group";

export async function getAllGroups() {
	return await fetchClient<Group>("/groups");
}

export async function getGroupById(id: string) {
	return await fetchClient<Group>(`/groups/${id}`);
}

export async function getGroupBySharedPasscode(sharedPasscode: string) {
	return await fetchClient<Group>(`/groups/passcode/${sharedPasscode}`);
}

export async function getGroupMembershipsByEditorAndGroupType(
	editorId: string,
	groupType: string,
): Promise<Group> {
	const group = await fetchClient<Group>(
		`/groups/?editorId=${editorId}&groupType=${groupType}`,
	);
	return group;
}
