// import { UserPreferences } from "@/app/services/userService";
// import type { AcademicYear, StudentAcademicYear } from "@/services/parentsServices";
import type { UserRole } from "@/app/api/auth/users/dao/user";
import type { User, UserPreferences } from "@/types";
// import type { UserPreferences } from "@/services/portal/user/userService";
// import type { UserLoggedI, UserPreferences } from "@/types/entities/user";
// import { getVersionInfo } from "@/utils/getVersionInfo";
// import { mergeArrays } from "@/utils/mergeArrays";
import { create } from "zustand";
import { createJSONStorage, persist } from "zustand/middleware";

export interface PortalState {
	user?: User;
	userRole?: UserRole;
	previouslyUserRole?: UserRole;
	userPreferences: UserPreferences;
	sidePanel?: unknown;
}

const initialState: PortalState = {
	user: undefined,
	sidePanel: undefined,
	userPreferences: {
		theme: "light",
		sidebar: {
			compact: false,
		},
	},
};

/**
 * For the use of certain states use the custom state handler hooks
 * like useUser and useUserPreferences
 * to get the state and set the state
 * they will be localled inside state-handlers folder
 */
const usePortalStore = create<PortalState>()(
	persist(
		(set) => ({
			...initialState,
		}),
		{
			name: "utmdia-portal",
			storage: createJSONStorage(() => sessionStorage),
		},
	),
);

export default usePortalStore;

export const setUser = (user: User) => {
	usePortalStore.setState({ user });
};
export const setUserRole = (userRole: UserRole) => {
	usePortalStore.setState({ userRole });
	usePortalStore.setState((state) =>
		state.userRole?.toString() !== "settings"
			? {
					previouslyUserRole: state.userRole,
				}
			: {},
	);

	usePortalStore.setState({
		userRole,
	});
};
export const setUserPreferences = (userPreferences: UserPreferences) => {
	usePortalStore.setState({ userPreferences });
};
export const setSidePanel = (sidePanel: unknown) => {
	usePortalStore.setState({ sidePanel });
};
