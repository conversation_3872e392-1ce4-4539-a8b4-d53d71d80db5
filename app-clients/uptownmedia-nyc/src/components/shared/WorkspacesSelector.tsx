"use client";

import type { UserRole } from "@/app/api/auth/users/dao/user";

// import useAppStore from '@/stores/students/useAppStore';
import {
	Button,
	Dropdown,
	DropdownItem,
	DropdownMenu,
	DropdownSection,
	DropdownTrigger,
	SelectItem,
	SelectSection,
	Spacer,
} from "@heroui/react";
import Link from "next/link";
import { redirect, useParams } from "next/navigation";
import React, { useLayoutEffect, useMemo, useState } from "react";

import LoadingContent from "../ui/LoadingContent";

import { Icon } from "./icons";

import Select, { selectItemClassName } from "@/components/ui/Select";
import usePortalStore, { setUserRole } from "@/stores/usePortalStore";
import { useLanguage } from "@/utils/i18n/LanguageContext";
import { useTranslation } from "@/utils/i18n/client";

type Workspace = {
	name: string;
	link: string;
	id: string;
};

type RoleToWorkspaceMap = Record<UserRole, Workspace>;
// user Roles: <PERSON><PERSON>, Candi<PERSON>, <PERSON>, Staff, Counselor,  Admission Officer, School Rep

function WorkspacesSelector({
	isCompact,
}: {
	workspace?: string;
	isCompact?: boolean;
}) {
	const params = useParams<{ role: string }>();
	const { role } = params;
	const userRole = usePortalStore((state) => state.userRole);
	const previouslyUserRole = usePortalStore(
		(state) => state.previouslyUserRole,
	);
	const selectedOption = useMemo(() => {
		return userRole !== undefined && role ? [userRole] : [];
	}, [userRole, role]);
	// const [selectedOption, setSelectedOption] = useState<string[]>(workspace !== undefined ? [workspace] : []);
	const [isLoading, setIsLoading] = useState(true);

	const { currentLanguage } = useLanguage();
	const { t } = useTranslation(currentLanguage, "workspaces");
	const workspacesName = t("roles");
	const user = usePortalStore((state) => state.user);

	useLayoutEffect(() => {
		if ((role && role !== userRole) || !previouslyUserRole) {
			setUserRole(role as UserRole);
		}
		setIsLoading(false);
	}, [role, userRole, previouslyUserRole]);

	const roleToWorkspaceMap: RoleToWorkspaceMap = useMemo(
		() => ({
			editor: {
				id: "editor",
				name: t("roleNames.editor"),
				link: "/portal/editor/articles",
			},
			author: {
				id: "author",
				name: t("roleNames.author"),
				link: "/portal/author/articles",
			},
			admin: {
				id: "admin",
				name: t("roleNames.admin"),
				link: "/portal/admin",
			},
			guest: {
				id: "guest",
				name: t("roleNames.guest"),
				link: "/portal/guest",
			},
			reader: {
				id: "reader",
				name: t("roleNames.reader"),
				link: "/portal/reader",
			},
			"super User": {
				id: "super-user",
				name: t("roleNames.super-user"),
				link: "/portal/super-user",
			},
		}),
		[t],
	);

	const workspaces = useMemo(() => {
		return (
			user?.roles?.map((role) => roleToWorkspaceMap[role]).filter(Boolean) || []
		);
	}, [user?.roles, roleToWorkspaceMap]);

	function handleSelect(value: string) {
		setUserRole(value as UserRole);
		const workspace = workspaces.find((workspace) => workspace.id === value);

		workspace && redirect(workspace.link);
	}

	if (isLoading) {
		return (
			<div className="w-full flex flex-col gap-1 px-5">
				<Spacer className="h-0" y={2} />
				{!isCompact && <LoadingContent />}
				{isCompact && (
					<Button
						aria-placeholder={workspacesName}
						className="min-w-0 px-2 py-3 border-thin rounded-lg border-foreground-900 h-auto animate-pulse"
						variant="bordered"
					>
						<Icon className="h-5 w-5" name="objects-column" />
					</Button>
				)}
			</div>
		);
	}

	return (
		userRole && (
			<div className="w-full flex flex-col gap-1 px-5">
				<Spacer className="h-0" y={2} />
				{!isCompact && (
					<Select
						aria-label={workspacesName}
						classNames={{
							trigger: "min-w-0 px-4 py-3 group-data-[theme=dark]:bg-content1",
							value: "leading-tight",
						}}
						selectedKeys={selectedOption}
						selectorIcon={
							<Icon className="text-primary-700" name="chevron-down" />
						}
						variant="bordered"
						onChange={(e) => handleSelect(e.target.value)}
					>
						<SelectSection title={workspacesName}>
							{workspaces.map((workspace) => (
								<SelectItem
									key={workspace.id}
									classNames={{
										base: selectItemClassName,
										title: "text-medium",
									}}
								>
									{workspace.name}
								</SelectItem>
							))}
						</SelectSection>
					</Select>
				)}
				{isCompact && (
					<Dropdown placement="right-start">
						<DropdownTrigger>
							<Button
								aria-placeholder={workspacesName}
								className="min-w-0 px-2 py-3 border rounded-lg border-foreground-900 h-auto"
								variant="bordered"
							>
								<Icon className="h-5 w-5" name="objects-column" />
							</Button>
						</DropdownTrigger>
						{workspaces?.length > 0 && (
							<DropdownMenu
								aria-label="Static Actions"
								className="md:text-base"
								selectedKeys={selectedOption}
								selectionMode="single"
								onAction={(selected) => handleSelect(selected as string)}
							>
								<DropdownSection title={workspacesName}>
									{workspaces.map((workspace, idx) => (
										<DropdownItem
											key={workspace.id}
											as={Link}
											className={selectItemClassName}
											href={workspace.link}
										>
											{workspace.name}
										</DropdownItem>
									))}
								</DropdownSection>
							</DropdownMenu>
						)}
					</Dropdown>
				)}
			</div>
		)
	);
}

export default WorkspacesSelector;
