"use client";
import { <PERSON><PERSON>, <PERSON>, CardBody, Input } from "@heroui/react";
import { Icon } from "@iconify/react";
import { getProviders, signIn } from "next-auth/react";
import Link from "next/link";
import type React from "react";
import { useEffect, useState } from "react";

export default function SignUp() {
	const [availableProviders, setAvailableProviders] = useState<string[]>([]);
	const [email, setEmail] = useState("");

	useEffect(() => {
		const loadProviders = async () => {
			const providers = await getProviders();

			if (providers) {
				setAvailableProviders(Object.keys(providers));
			}
		};

		loadProviders();
	}, []);

	const handleEmailSignUp = (e: React.FormEvent) => {
		e.preventDefault();
		signIn("credentials", {
			email,
			redirect: true,
		});
	};

	return (
		<div className="flex items-center justify-center min-h-screen p-4 bg-gray-100">
			<Card className="w-full max-w-md p-8 bg-white rounded-lg shadow-lg">
				<CardBody className="space-y-6">
					<div className="text-center">
						<h1 className="text-2xl font-bold text-gray-900">Sign up</h1>
					</div>
					<form onSubmit={handleEmailSignUp} className="space-y-4">
						<div>
							<label
								htmlFor="email"
								className="block mb-1 text-sm font-medium text-gray-700"
							>
								Email
							</label>
							<Input
								id="email"
								type="email"
								value={email}
								onChange={(e) => setEmail(e.target.value)}
								placeholder="<EMAIL>"
								className="w-full"
								classNames={{
									input: "bg-gray-50 border-gray-300",
									inputWrapper: "bg-gray-50 border-gray-300",
								}}
								required
							/>
						</div>

						<Button
							type="submit"
							className="w-full text-white bg-black hover:bg-gray-800"
							size="lg"
						>
							Continue with email
						</Button>
					</form>

					<div className="relative">
						<div className="absolute inset-0 flex items-center">
							<div className="w-full border-t border-gray-300" />
						</div>
						<div className="relative flex justify-center text-sm">
							<span className="px-2 text-gray-500 bg-white">or</span>
						</div>
					</div>
					<div className="space-y-3">
						{availableProviders.includes("google") && (
							<Button
								className="w-full text-gray-700 bg-white border border-gray-300 hover:bg-gray-50"
								size="lg"
								startContent={
									<Icon className="text-xl" icon="logos:google-icon" />
								}
								variant="bordered"
								onPress={() => signIn("google")}
							>
								Continue with Google
							</Button>
						)}

						{availableProviders.includes("facebook") && (
							<Button
								className="w-full text-gray-700 bg-white border border-gray-300 hover:bg-gray-50"
								size="lg"
								startContent={
									<Icon className="text-xl" icon="logos:facebook" />
								}
								variant="bordered"
								onPress={() => signIn("facebook")}
							>
								Continue with Facebook
							</Button>
						)}

						{availableProviders.includes("apple") && (
							<Button
								className="w-full text-gray-700 bg-white border border-gray-300 hover:bg-gray-50"
								size="lg"
								startContent={<Icon className="text-xl" icon="logos:apple" />}
								variant="bordered"
								onPress={() => signIn("apple")}
							>
								Continue with Apple
							</Button>
						)}
					</div>
					<div className="text-center">
						<p className="text-xs text-gray-500">
							By clicking "Create account" above, you acknowledge that you will
							receive updates from the Resume team and that you have read,
							understood, and agree to Resume Library's{" "}
							<Link
								href="/terms-service"
								className="text-blue-600 hover:text-blue-500"
							>
								Terms & Conditions
							</Link>
							,{" "}
							<Link
								href="/privacy-policy"
								className="text-blue-600 hover:text-blue-500"
							>
								LinkedIn Agreement
							</Link>{" "}
							and{" "}
							<Link
								href="/privacy-policy"
								className="text-blue-600 hover:text-blue-500"
							>
								Privacy Policy
							</Link>
							.
						</p>
					</div>
					<div className="text-center">
						<p className="text-sm text-gray-600">
							Already have an account?{" "}
							<Link
								href="/auth/sign-in"
								className="font-medium text-blue-600 hover:text-blue-500"
							>
								Log in
							</Link>
						</p>
					</div>
				</CardBody>
			</Card>
		</div>
	);
}
