"use client";
import { <PERSON><PERSON>, <PERSON>, CardBody, Input } from "@heroui/react";
import { Icon } from "@iconify/react";
import { getProviders, signIn } from "next-auth/react";
import Link from "next/link";
import type React from "react";
import { useEffect, useState } from "react";

export default function Login() {
	const [availableProviders, setAvailableProviders] = useState<string[]>([]);
	const [email, setEmail] = useState("");
	const [password, setPassword] = useState("");

	useEffect(() => {
		const loadProviders = async () => {
			const providers = await getProviders();

			if (providers) {
				setAvailableProviders(Object.keys(providers));
			}
		};

		loadProviders();
	}, []);

	const handleEmailLogin = (e: React.FormEvent) => {
		e.preventDefault();
		signIn("credentials", {
			email,
			redirect: true,
		});
	};

	return (
		<div className="flex items-center justify-center min-h-screen p-4 bg-gray-100">
			<Card className="w-full max-w-md p-8 bg-white rounded-lg shadow-lg">
				<CardBody className="space-y-6">
					<div className="text-center">
						<h1 className="text-2xl font-bold text-gray-900">Log in</h1>
					</div>

					<form onSubmit={handleEmailLogin} className="space-y-4">
						<div>
							<label
								htmlFor="email"
								className="block mb-1 text-sm font-medium text-gray-700"
							>
								Email
							</label>
							<Input
								id="email"
								type="email"
								value={email}
								onChange={(e) => setEmail(e.target.value)}
								placeholder="<EMAIL>"
								className="w-full"
								classNames={{
									input: "bg-gray-50 border-gray-300",
									inputWrapper: "bg-gray-50 border-gray-300",
								}}
								required
							/>
						</div>

						<div>
							<div className="flex items-center justify-between mb-1">
								<label
									htmlFor="password"
									className="block text-sm font-medium text-gray-700"
								>
									Password
								</label>
								<Link
									href="/auth/forgot-password"
									className="text-sm text-black hover:text-black/80"
								>
									Forgot password?
								</Link>
							</div>
							<Input
								id="password"
								type="password"
								value={password}
								onChange={(e) => setPassword(e.target.value)}
								placeholder="Password"
								className="w-full"
								classNames={{
									input: "bg-gray-50 border-gray-300",
									inputWrapper: "bg-gray-50 border-gray-300",
								}}
								required
							/>
						</div>

						<Button
							type="submit"
							className="w-full text-white bg-black hover:bg-gray-800"
							size="lg"
						>
							Log in
						</Button>
					</form>

					{/* Divider */}
					<div className="relative">
						<div className="absolute inset-0 flex items-center">
							<div className="w-full border-t border-gray-300" />
						</div>
						<div className="relative flex justify-center text-sm">
							<span className="px-2 text-gray-500 bg-white">or</span>
						</div>
					</div>

					{/* Social Login */}
					<div className="space-y-3">
						{availableProviders.includes("google") && (
							<Button
								className="w-full text-gray-700 bg-white border border-gray-300 hover:bg-gray-50"
								size="lg"
								startContent={
									<Icon className="text-xl" icon="logos:google-icon" />
								}
								variant="bordered"
								onPress={() => signIn("google")}
							>
								Login with Google
							</Button>
						)}

						{availableProviders.includes("facebook") && (
							<Button
								className="w-full text-gray-700 bg-white border border-gray-300 hover:bg-gray-50"
								size="lg"
								startContent={
									<Icon className="text-xl" icon="logos:facebook" />
								}
								variant="bordered"
								onPress={() => signIn("facebook")}
							>
								Login with Facebook
							</Button>
						)}

						{availableProviders.includes("apple") && (
							<Button
								className="w-full text-gray-700 bg-white border border-gray-300 hover:bg-gray-50"
								size="lg"
								startContent={<Icon className="text-xl" icon="logos:apple" />}
								variant="bordered"
								onPress={() => signIn("apple")}
							>
								Login with Apple
							</Button>
						)}
					</div>

					{/* Footer */}
					<div className="text-center">
						<p className="text-sm text-gray-600">
							Don't have an account?{" "}
							<Link
								href="/auth/sign-up"
								className="font-medium text-black hover:text-black/80"
							>
								Sign up for free
							</Link>
						</p>
					</div>
				</CardBody>
			</Card>
		</div>
	);
}
