"use client";

import ModalLayout from "@/components/shared/info-modal/ModalLayout";
import usePortalStore from "@/stores/usePortalStore";
import { compareObjects } from "@/utils/compareObjects";
import {
	Avatar,
	Button,
	Divider,
	Input,
	Select,
	SelectItem,
} from "@heroui/react";
import { zodResolver } from "@hookform/resolvers/zod";
import type React from "react";
import { useCallback, useEffect, useMemo, useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";

const countries = [
	{ label: "United States", value: "United States" },
	{ label: "Canada", value: "Canada" },
	{ label: "United Kingdom", value: "United Kingdom" },
	{ label: "Australia", value: "Australia" },
	{ label: "Germany", value: "Germany" },
	{ label: "France", value: "France" },
	{ label: "Japan", value: "Japan" },
];

const profileSchema = z.object({
	fullName: z.string().min(1, "Full name is required"),
	phoneNumber: z.string().min(1, "Phone number is required"),
	country: z.string().min(1, "Country is required"),
	address: z.string().min(1, "Address is required"),
	city: z.string().min(1, "City is required"),
	postalCode: z.string().min(1, "Postal code is required"),
});

type ProfileFormData = z.infer<typeof profileSchema>;
const emptyProfile: ProfileFormData = {
	fullName: "",
	phoneNumber: "",
	country: "",
	address: "",
	city: "",
	postalCode: "",
};

export const ProfileSettings: React.FC = () => {
	const [initialProfile, setInitialProfile] =
		useState<ProfileFormData>(emptyProfile);
	const user = usePortalStore((state) => state.user);
	const {
		register,
		handleSubmit,
		setValue,
		formState: { errors, isValid, isDirty, isSubmitting },
		reset,
		clearErrors,
		watch,
	} = useForm<ProfileFormData>({
		resolver: zodResolver(profileSchema),
		defaultValues: initialProfile,
	});
	const currentValues = watch();

	useEffect(() => {
		const fakeApiResponse = {
			fullName: "Edwin Cruz",
			phoneNumber: "+****************",
			country: "United States",
			address: "123 Main St",
			city: "New York",
			postalCode: "10001",
		};
		setInitialProfile(fakeApiResponse);
		reset(fakeApiResponse);

		return () => {
			setInitialProfile(emptyProfile);
			reset(emptyProfile);
			clearErrors();
		};
	}, [reset, clearErrors]);

	const hasChanges = useCallback(() => {
		return !compareObjects(initialProfile, currentValues);
	}, [currentValues, initialProfile]);

	const onSubmit = (data: ProfileFormData) => {
		console.log("Saving profile data:", data);
	};

	return (
		<ModalLayout title={"Profile"}>
			<form onSubmit={handleSubmit(onSubmit)}>
				<div className="flex items-center gap-4 mb-8">
					<Avatar name="EC" size="lg" className="w-20 h-20 text-2xl" />
					<div>
						<h3 className="text-lg font-medium">Photo</h3>
						<p className="text-default-500 text-sm">Role</p>
					</div>
				</div>

				<div className="mb-8">
					<h3 className="text-lg font-medium mb-4">Personal Info</h3>

					<div className="grid grid-cols-1 md:grid-cols-2_ gap-4">
						<div>
							<Input
								label={"Fullname"}
								{...register("fullName")}
								variant="bordered"
								errorMessage={errors.fullName?.message}
								isInvalid={!!errors.fullName}
							/>
							<p className="text-default-500 text-xs mt-1">Fullname</p>
						</div>

						<div>
							<Input
								label={"Phone Number"}
								{...register("phoneNumber")}
								variant="bordered"
								startContent={
									<div className="flex items-center">
										<span className="text-default-400 text-small">+1</span>
									</div>
								}
								isRequired
								errorMessage={errors.phoneNumber?.message}
								isInvalid={!!errors.phoneNumber}
							/>
						</div>
					</div>
				</div>

				<div className="mb-8">
					<h3 className="text-lg font-medium mb-4">Address</h3>

					<div className="grid grid-cols-1 md:grid-cols-2_ gap-4 mb-4">
						<div>
							<Select
								label={"Country"}
								{...register("country")}
								variant="bordered"
								errorMessage={errors.country?.message}
								isInvalid={!!errors.country}
							>
								{countries.map((country) => (
									<SelectItem key={country.value}>{country.label}</SelectItem>
								))}
							</Select>
						</div>

						<div>
							<Input
								label={"Address"}
								{...register("address")}
								variant="bordered"
								errorMessage={errors.address?.message}
								isInvalid={!!errors.address}
							/>
						</div>
					</div>

					<div className="grid grid-cols-1 md:grid-cols-2_ gap-4">
						<div>
							<Input
								label={"City"}
								{...register("city")}
								variant="bordered"
								errorMessage={errors.city?.message}
								isInvalid={!!errors.city}
							/>
						</div>

						<div>
							<Input
								label={"Postal Code"}
								{...register("postalCode")}
								variant="bordered"
								errorMessage={errors.postalCode?.message}
								isInvalid={!!errors.postalCode}
							/>
						</div>
					</div>
				</div>

				<div className="mb-8">
					<Button
						color="primary"
						variant="flat"
						type="submit"
						isDisabled={isSubmitting || !isValid || !hasChanges()}
					>
						Save
					</Button>
				</div>

				<Divider className="my-8" />

				<div>
					<h3 className="text-lg font-medium mb-4">Account</h3>
					<Button
						color="danger"
						variant="solid"
						onPress={() => console.log("Delete Account")}
					>
						Delete Account
					</Button>
				</div>
			</form>
		</ModalLayout>
	);
};
