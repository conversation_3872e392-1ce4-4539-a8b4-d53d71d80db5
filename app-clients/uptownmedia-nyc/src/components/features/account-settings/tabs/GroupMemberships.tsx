"use client";

import type { GroupResponse } from "@/app/api/groups/dto/groupResponse";

import { Button } from "@heroui/button";
import { Input } from "@heroui/input";
import {
	Checkbox,
	Dropdown,
	DropdownItem,
	DropdownMenu,
	DropdownTrigger,
	Modal,
	ModalBody,
	ModalContent,
	Modal<PERSON>ooter,
	ModalHeader,
	Select,
	SelectItem,
	useDisclosure,
} from "@heroui/react";
import React, { useEffect, useState } from "react";

import { UserRole } from "@/app/api/auth/users/dao/user";
import ContentWrapperBase from "@/components/shared/ContentWrapperBase";
import { getGroupMembershipsByEditorAndGroupType } from "@/services/groupServices";
import usePortalStore from "@/stores/usePortalStore";
import { useLanguage } from "@/utils/i18n/LanguageContext";
import { useTranslation } from "@/utils/i18n/client";

const GROUP_TYPES = [
	{ key: "news", label: "News" },
	{ key: "sports", label: "Sports" },
	{ key: "entertainment", label: "Entertainment" },
	{ key: "business", label: "Business" },
	{ key: "technology", label: "Technology" },
	{ key: "health", label: "Health" },
	{ key: "lifestyle", label: "Lifestyle" },
	{ key: "team", label: "Team" },
	{ key: "family", label: "Family" },
	{ key: "other", label: "Other" },
];

const SUBSCRIPTION_PLANS = [
	{ key: "basic", label: "Basic" },
	{ key: "premium", label: "Premium" },
	{ key: "enterprise", label: "Enterprise" },
];

const SUBSCRIPTION_STATUS = [
	{ key: "active", label: "Active" },
	{ key: "inactive", label: "Inactive" },
	{ key: "pending", label: "Pending" },
	{ key: "cancelled", label: "Cancelled" },
];

interface GroupFormData {
	group_name: string;
	group_type: string;
	shared_passcode: string;
	subscription_id?: string;
	subscription_plan?: string;
	subscription_status?: string;
}

function GroupMemberships({ groupType }: { groupType: string }) {
	const [inviteEmail, setInviteEmail] = useState("");
	const [domainInput, setDomainInput] = useState("");
	const [restrictedDomains, setRestrictedDomains] = useState<string[]>([]);
	const generatedInvitationLink =
		"https://www.msgmason.com/invitation?q=team+members=c...";
	const user = usePortalStore((state) => state.user);
	// const currentWorkspace = useAppStore((state) => state.selectedWorkspace);
	const [userGroup, setUserGroup] = useState<GroupResponse>();
	const { currentLanguage } = useLanguage();
	const { t } = useTranslation(currentLanguage, "common");

	// Modal and form states
	const { isOpen, onOpen, onOpenChange } = useDisclosure();
	const [isCreatingGroup, setIsCreatingGroup] = useState(false);
	const [createGroupError, setCreateGroupError] = useState("");
	const [createGroupSuccess, setCreateGroupSuccess] = useState("");
	const [formData, setFormData] = useState<GroupFormData>({
		group_name: "",
		group_type: groupType,
		shared_passcode: "",
		subscription_id: "",
		subscription_plan: "",
		subscription_status: "",
	});

	useEffect(() => {
		if (user?.id) {
			getGroupMembershipsByEditorAndGroupType(user.id, groupType)
				.then((response) => {
					// biome-ignore lint/suspicious/noExplicitAny: <explanation>
					setUserGroup((response as any) || {});
				})
				.catch((error) => {
					console.error("Error fetching group memberships:", error);
					setUserGroup({} as GroupResponse);
				});
		}
	}, [user?.id, groupType]);

	const isEditor = user?.roles?.includes(UserRole.EDITOR) || false;
	const hasGroup = userGroup && Object.keys(userGroup).length > 0;

	const handleCopyLink = () => {
		navigator.clipboard.writeText(generatedInvitationLink);
	};

	const handleCopyGroupCode = () => {
		if (userGroup?.shared_passcode) {
			navigator.clipboard.writeText(userGroup.shared_passcode);
		}
	};

	const handleSetDomain = () => {
		if (domainInput && !restrictedDomains.includes(domainInput)) {
			setRestrictedDomains([...restrictedDomains, domainInput]);
			setDomainInput("");
		}
	};

	const handleInvite = () => {
		// Handle invitation logic
		setInviteEmail("");
	};

	const generatePasscode = () => {
		const getRandomInt = (min: number, max: number): number => {
			const range = max - min;
			const randomBuffer = new Uint32Array(1);

			crypto.getRandomValues(randomBuffer);

			return min + (randomBuffer[0] % range);
		};

		const randomLetters = (length: number): string => {
			const letters = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";

			return Array.from({ length }, () =>
				letters.charAt(getRandomInt(0, letters.length)),
			).join("");
		};

		const code = `${getRandomInt(1000, 9999)}-${randomLetters(4)}-${randomLetters(3)}`;

		setFormData((prev) => ({ ...prev, shared_passcode: code }));
	};

	const handleInputChange = (field: keyof GroupFormData, value: string) => {
		setFormData((prev) => ({ ...prev, [field]: value }));
		setCreateGroupError("");
		setCreateGroupSuccess("");
	};

	const handleCreateGroup = async () => {
		setIsCreatingGroup(true);
		setCreateGroupError("");

		if (!formData.group_name.trim()) {
			setCreateGroupError("Group name is required");
			setIsCreatingGroup(false);

			return;
		}

		if (!formData.shared_passcode.trim()) {
			setCreateGroupError("Shared passcode is required");
			setIsCreatingGroup(false);

			return;
		}

		try {
			const apiData = {
				editor_id: user?.id || user?.email || "unknown",
				group_name: formData.group_name.trim(),
				group_type: formData.group_type,
				shared_passcode: formData.shared_passcode.trim(),
				subscription_id: formData.subscription_id || undefined,
				subscription_plan: formData.subscription_plan || undefined,
				subscription_status: formData.subscription_status || undefined,
			};

			const response = await fetch("/api/groups", {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
				body: JSON.stringify(apiData),
			});

			if (response.ok) {
				// Create a mock group object with the data we just created
				// const mockCreatedGroup = {
				//   id: crypto.randomUUID(),
				//   tenant_id: `tenant-${Date.now()}-${user?.id}`,
				//   editor_id: user?.id,
				//   group_name: formData.group_name.trim(),
				//   shared_passcode: formData.shared_passcode.trim(),
				//   group_type: formData.group_type,
				//   subscription_id: formData.subscription_id || undefined,
				//   subscription_plan: formData.subscription_plan || undefined,
				//   subscription_status: formData.subscription_status || undefined,
				//   created_at: Math.floor(Date.now() / 1000),
				//   expires_at: Math.floor(Date.now() / 1000) + 365 * 24 * 60 * 60, // 1 year from now
				//   memberships: [
				//     {
				//       id: crypto.randomUUID(),
				//       tenant_id: `tenant-${Date.now()}-${user?.id}`,
				//       user_id: user?.id,
				//       requested_at: Math.floor(Date.now() / 1000),
				//       approved: true,
				//       approved_at: Math.floor(Date.now() / 1000),
				//     },
				//   ],
				// };

				// Update the local state with the created group
				// setUserGroup(mockCreatedGroup as GroupResponse);

				// Set success message
				setCreateGroupSuccess(
					`Group "${formData.group_name.trim()}" created successfully!`,
				);

				// Reset form
				setFormData({
					group_name: "",
					group_type: groupType,
					shared_passcode: "",
					subscription_id: "",
					subscription_plan: "",
					subscription_status: "",
				});

				// Close modal after a short delay to show success message
				setTimeout(() => {
					onOpenChange();
					setCreateGroupSuccess("");
				}, 1500);

				// Note: In a real app with persistent storage, we would refresh from the server:
				// const updatedGroup = await getGroupMembershipsByEditorAndGroupType(user.id, groupType);
				// setUserGroup(updatedGroup as GroupResponse);

				// For now, we don't need to refresh since we're updating the state manually above
			} else {
				const data = await response.json();

				setCreateGroupError(
					data.error || data.message || "Failed to create group",
				);
			}
		} catch (error) {
			console.error("Error creating group:", error);
			setCreateGroupError("An unexpected error occurred. Please try again.");
		} finally {
			setIsCreatingGroup(false);
		}
	};

	return (
		<ContentWrapperBase
			className="border-none"
			title={t(`${groupType}-memberships`)}
		>
			<div className="p-6 ">
				<div className="space-y-8">
					{isEditor && (
						<section className="p-6 border rounded-lg bg-gray-50">
							<h2 className="mb-4 text-xl font-semibold">
								{hasGroup ? "Your Group" : "Create Your Group"}
							</h2>

							{!hasGroup ? (
								<div className="space-y-4">
									<p className="text-default-500">
										As an editor, you can create and manage your own group.
										Create a group to invite team members and manage content
										collaboratively.
									</p>
									<Button
										className="text-white"
										color="primary"
										onPress={() => {
											onOpen();
											setCreateGroupError("");
											setCreateGroupSuccess("");
										}}
									>
										Create New Group
									</Button>
								</div>
							) : (
								<div className="space-y-4">
									<div className="grid grid-cols-1 gap-4 md:grid-cols-2">
										<div>
											<h3 className="text-lg font-semibold">
												{userGroup?.group_name}
											</h3>
											<p className="text-default-500">
												Type: {userGroup?.group_type}
											</p>
											<p className="text-default-500">
												Members: {userGroup?.memberships?.length || 0}
											</p>
										</div>
										<div>
											<label
												className="block mb-2 text-sm font-medium"
												htmlFor="group-code-input"
											>
												Group Code
											</label>
											<div className="flex gap-2">
												<Input
													readOnly
													className="flex-1"
													id="group-code-input"
													value={userGroup?.shared_passcode || ""}
												/>
												<Button
													className="text-white"
													color="primary"
													onPress={handleCopyGroupCode}
												>
													<span className="flex items-center gap-2">
														{/* biome-ignore lint/a11y/noSvgWithoutTitle: <explanation> */}
														<svg
															fill="none"
															height="20"
															viewBox="0 0 24 24"
															width="20"
															xmlns="http://www.w3.org/2000/svg"
														>
															<path
																d="M8 8H6C4.89543 8 4 8.89543 4 10V18C4 19.1046 4.89543 20 6 20H14C15.1046 20 16 19.1046 16 18V16M8 8V6C8 4.89543 8.89543 4 10 4H18C19.1046 4 20 4.89543 20 6V14C20 15.1046 19.1046 16 18 16H16M8 8H14C15.1046 8 16 8.89543 16 10V16"
																stroke="currentColor"
																strokeLinecap="round"
																strokeLinejoin="round"
																strokeWidth="2"
															/>
														</svg>
														Copy Code
													</span>
												</Button>
											</div>
											<p className="mt-1 text-xs text-default-500">
												Share this code with team members to join your group
											</p>
										</div>
									</div>
								</div>
							)}
						</section>
					)}
					<section>
						<h2 className="mb-2 text-xl font-semibold">
							{t("invite-by-link")}
						</h2>
						<p className="mb-4 text-default-500">
							{t("share-link-with-members")}
						</p>
						<div className="flex gap-2">
							<Input
								readOnly
								className="flex-1"
								value={generatedInvitationLink}
							/>
							<Button
								className="text-white"
								color="primary"
								onPress={handleCopyLink}
							>
								<span className="flex items-center gap-2">
									{/* biome-ignore lint/a11y/noSvgWithoutTitle: <explanation> */}
									<svg
										fill="none"
										height="20"
										viewBox="0 0 24 24"
										width="20"
										xmlns="http://www.w3.org/2000/svg"
									>
										<path
											d="M8 8H6C4.89543 8 4 8.89543 4 10V18C4 19.1046 4.89543 20 6 20H14C15.1046 20 16 19.1046 16 18V16M8 8V6C8 4.89543 8.89543 4 10 4H18C19.1046 4 20 4.89543 20 6V14C20 15.1046 19.1046 16 18 16H16M8 8H14C15.1046 8 16 8.89543 16 10V16"
											stroke="currentColor"
											strokeLinecap="round"
											strokeLinejoin="round"
											strokeWidth="2"
										/>
									</svg>
									{t("copy-link")}
								</span>
							</Button>
						</div>
					</section>

					{/* Invite by email section */}
					<section>
						<h2 className="mb-2 text-xl font-semibold">
							{t("invite-by-email")}
						</h2>
						<p className="mb-4 text-default-500">
							{t("email-invite-validity")}
						</p>
						<div className="flex gap-2">
							<Input
								className="flex-1"
								placeholder={t("enter-email-placeholder")}
								type="email"
								value={inviteEmail}
								onChange={(e) => setInviteEmail(e.target.value)}
							/>
							<Button
								className="text-white"
								color="primary"
								onPress={handleInvite}
							>
								{t("invite")}
							</Button>
						</div>
						<p className="mt-1 text-sm text-default-500">
							{t("send-invitation-one-at-time")}
						</p>
					</section>

					{/* Group memberships list */}
					<section className="border rounded-lg">
						<div className="flex items-center justify-between p-4 border-b">
							<div className="flex items-center gap-2">
								<Checkbox />
								<h3 className="text-lg font-semibold">
									{t(`${groupType.toLowerCase()}-memberships`)}
								</h3>
							</div>
							<Dropdown>
								<DropdownTrigger>
									<Button className="min-w-unit-24" variant="light">
										{t("actions")}
										{/* biome-ignore lint/a11y/noSvgWithoutTitle: <explanation> */}
										<svg
											fill="none"
											height="16"
											viewBox="0 0 24 24"
											width="16"
											xmlns="http://www.w3.org/2000/svg"
										>
											<path
												d="M6 9L12 15L18 9"
												stroke="currentColor"
												strokeLinecap="round"
												strokeLinejoin="round"
												strokeWidth="2"
											/>
										</svg>
									</Button>
								</DropdownTrigger>
								<DropdownMenu aria-label="Membership actions">
									<DropdownItem key={"action-remove"}>
										{t("remove")}
									</DropdownItem>
									<DropdownItem key={"action-approve"}>
										{t("approve")}
									</DropdownItem>
								</DropdownMenu>
							</Dropdown>
						</div>

						{/* Membership rows */}
						<div className="divide-y">
							{userGroup?.memberships?.map((membership, idx) => (
								<div
									key={`group-${membership?.id}-${idx}`}
									className="flex items-center justify-between p-4"
								>
									<div className="flex items-center gap-4">
										<Checkbox isDisabled={membership.userId === user?.id} />
										<span>{membership.userId}</span>
									</div>

									<span
										className={`px-3 py-1 rounded-full text-sm ${
											membership.approved
												? "bg-green-100 text-green-700"
												: "bg-yellow-100 text-yellow-700"
										}`}
									>
										{membership.approved ? t("approved") : t("pending")}
									</span>
								</div>
							))}
						</div>
					</section>
				</div>
			</div>

			{/* Create Group Modal */}
			<Modal isOpen={isOpen} size="2xl" onOpenChange={onOpenChange}>
				<ModalContent>
					{(onClose) => (
						<>
							<ModalHeader className="flex flex-col gap-1">
								Create New Group
							</ModalHeader>
							<ModalBody>
								{createGroupError && (
									<div className="p-4 text-red-700 bg-red-100 border border-red-300 rounded-md">
										{createGroupError}
									</div>
								)}

								{createGroupSuccess && (
									<div className="p-4 text-green-700 bg-green-100 border border-green-300 rounded-md">
										{createGroupSuccess}
									</div>
								)}

								<div className="space-y-4">
									<Input
										isRequired
										label="Group Name"
										placeholder="Enter group name"
										value={formData.group_name}
										variant="bordered"
										onChange={(e) =>
											handleInputChange("group_name", e.target.value)
										}
									/>

									<Select
										isRequired
										label="Group Type"
										placeholder="Select group type"
										selectedKeys={
											formData.group_type ? [formData.group_type] : []
										}
										variant="bordered"
										onSelectionChange={(keys) => {
											const selectedKey = Array.from(keys)[0] as string;

											handleInputChange("group_type", selectedKey || "");
										}}
									>
										{GROUP_TYPES.map((type) => (
											<SelectItem key={type.key}>{type.label}</SelectItem>
										))}
									</Select>

									<div className="space-y-2">
										<Input
											isRequired
											endContent={
												<Button
													className="text-xs"
													size="sm"
													variant="flat"
													onPress={generatePasscode}
												>
													Generate
												</Button>
											}
											label="Shared Passcode"
											placeholder="Enter or generate passcode"
											value={formData.shared_passcode}
											variant="bordered"
											onChange={(e) =>
												handleInputChange("shared_passcode", e.target.value)
											}
										/>
										<p className="text-xs text-gray-500">
											This passcode will be used by members to join the group
										</p>
									</div>

									<Input
										label="Subscription ID (Optional)"
										placeholder="Enter subscription ID"
										value={formData.subscription_id || ""}
										variant="bordered"
										onChange={(e) =>
											handleInputChange("subscription_id", e.target.value)
										}
									/>

									<Select
										label="Subscription Plan (Optional)"
										placeholder="Select subscription plan"
										selectedKeys={
											formData.subscription_plan
												? [formData.subscription_plan]
												: []
										}
										variant="bordered"
										onSelectionChange={(keys) => {
											const selectedKey = Array.from(keys)[0] as string;

											handleInputChange("subscription_plan", selectedKey || "");
										}}
									>
										{SUBSCRIPTION_PLANS.map((plan) => (
											<SelectItem key={plan.key}>{plan.label}</SelectItem>
										))}
									</Select>

									<Select
										label="Subscription Status (Optional)"
										placeholder="Select subscription status"
										selectedKeys={
											formData.subscription_status
												? [formData.subscription_status]
												: []
										}
										variant="bordered"
										onSelectionChange={(keys) => {
											const selectedKey = Array.from(keys)[0] as string;

											handleInputChange(
												"subscription_status",
												selectedKey || "",
											);
										}}
									>
										{SUBSCRIPTION_STATUS.map((status) => (
											<SelectItem key={status.key}>{status.label}</SelectItem>
										))}
									</Select>
								</div>
							</ModalBody>
							<ModalFooter>
								<Button variant="bordered" onPress={onClose}>
									Cancel
								</Button>
								<Button
									className="text-white"
									color="primary"
									isLoading={isCreatingGroup}
									onPress={handleCreateGroup}
								>
									{isCreatingGroup ? "Creating..." : "Create Group"}
								</Button>
							</ModalFooter>
						</>
					)}
				</ModalContent>
			</Modal>
		</ContentWrapperBase>
	);
}

export default GroupMemberships;
