"use client";

import type { GroupResponse } from "@/app/api/groups/dto/groupResponse";
import type { GroupMembershipResponse } from "@/app/api/group-memberships/dto/groupMembershipResponse";

import { Button } from "@heroui/button";
import { Input } from "@heroui/input";
import {
	Checkbox,
	Dropdown,
	DropdownItem,
	DropdownMenu,
	DropdownTrigger,
	Modal,
	ModalBody,
	ModalContent,
	ModalFooter,
	ModalHeader,
	useDisclosure,
} from "@heroui/react";
import React, { useEffect, useState } from "react";

import { UserRole } from "@/app/api/auth/users/dao/user";
import ContentWrapperBase from "@/components/shared/ContentWrapperBase";
import { getGroupMembershipsByEditorAndGroupType } from "@/services/groupServices";
import usePortalStore from "@/stores/usePortalStore";
import { useLanguage } from "@/utils/i18n/LanguageContext";
import { useTranslation } from "@/utils/i18n/client";
import { fetchClient } from "@/utils/fetchClient";
import { useSession } from "next-auth/react";

interface GroupFormData {
	group_name: string;
	invite_code: string;
}

function GroupMemberships({ groupType }: { groupType: string }) {
	const [inviteEmail, setInviteEmail] = useState("");
	const [domainInput, setDomainInput] = useState("");
	const [restrictedDomains, setRestrictedDomains] = useState<string[]>([]);
	const generatedInvitationLink =
		"https://www.msgmason.com/invitation?q=team+members=c...";
	const user = usePortalStore((state) => state.user);
	const session = useSession();
	// const currentWorkspace = useAppStore((state) => state.selectedWorkspace);
	const [userGroup, setUserGroup] = useState<GroupResponse>();
	const [groupMembers, setGroupMembers] = useState<GroupMembershipResponse[]>([]);
	const [isLoadingMembers, setIsLoadingMembers] = useState(false);
	const { currentLanguage } = useLanguage();
	const { t } = useTranslation(currentLanguage, "common");
	const [isLoading, setIsLoading] = useState(true);
	// Modal and form states
	const { isOpen, onOpen, onOpenChange } = useDisclosure();
	const [isCreatingGroup, setIsCreatingGroup] = useState(false);
	const [createGroupError, setCreateGroupError] = useState("");
	const [createGroupSuccess, setCreateGroupSuccess] = useState("");
	const [formData, setFormData] = useState<GroupFormData>({
		group_name: "",
		invite_code: "",
	});

	if (session.status === "unauthenticated") return <div>unauthenticated</div>;
	useEffect(() => {
		console.log(session);
		if(session.data?.user?.id){
			getGroupMembershipsByEditorAndGroupType(session.data?.user?.id, "")
				.then((response) => {
					const group = response[0];
					// biome-ignore lint/suspicious/noExplicitAny: <explanation>
					setUserGroup(group);
					console.log("Group memberships:", response);

					// If group exists and has a shared passcode, fetch its members
					if (group && group.shared_passcode) {
						console.log("Fetching members for group with passcode:", group.shared_passcode);
						fetchGroupMembers(group.shared_passcode);
					} else {
						console.log("No group or shared passcode found:", group);
					}

					setIsLoading(false);
				})
				.catch((error) => {
					console.error("Error fetching group memberships:", error);
					setUserGroup({} as GroupResponse);
					setGroupMembers([]);
					setIsLoading(false);
				});

		}
	}, [session]);

	if (isLoading) {
		return <div>Loading...</div>;
	}

	const isEditor = user?.roles?.includes(UserRole.EDITOR) || false;
	const hasGroup = userGroup && Object.keys(userGroup).length > 0;

	// Function to fetch group members
	const fetchGroupMembers = async (sharedPasscode: string) => {
		setIsLoadingMembers(true);
		try {
			const response = await fetchClient<{ memberships: GroupMembershipResponse[] }>(`/groups/${sharedPasscode}/members`);
			console.log("Group members response:", response);
			setGroupMembers(response.memberships || []);
		} catch (error) {
			console.error("Error fetching group members:", error);
			setGroupMembers([]);
		} finally {
			setIsLoadingMembers(false);
		}
	};
	console.log("User group:", userGroup);
	console.log("Group members:", groupMembers);
	console.log("Is loading members:", isLoadingMembers);

	const handleCopyLink = () => {
		navigator.clipboard.writeText(generatedInvitationLink);
	};

	const handleCopyGroupCode = () => {
		if (userGroup?.shared_passcode) {
			navigator.clipboard.writeText(userGroup.shared_passcode);
		}
	};

	const handleSetDomain = () => {
		if (domainInput && !restrictedDomains.includes(domainInput)) {
			setRestrictedDomains([...restrictedDomains, domainInput]);
			setDomainInput("");
		}
	};

	const handleInvite = () => {
		// Handle invitation logic
		setInviteEmail("");
	};

	const generateInviteCode = () => {
		const getRandomInt = (min: number, max: number): number => {
			const range = max - min;
			const randomBuffer = new Uint32Array(1);

			crypto.getRandomValues(randomBuffer);

			return min + (randomBuffer[0] % range);
		};

		const randomLetters = (length: number): string => {
			const letters = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";

			return Array.from({ length }, () =>
				letters.charAt(getRandomInt(0, letters.length)),
			).join("");
		};

		const code = `${getRandomInt(1000, 9999)}-${randomLetters(4)}-${randomLetters(3)}`;

		setFormData((prev) => ({ ...prev, invite_code: code }));
	};

	const handleInputChange = (field: keyof GroupFormData, value: string) => {
		setFormData((prev) => ({ ...prev, [field]: value }));
		setCreateGroupError("");
		setCreateGroupSuccess("");
	};

	const handleCreateGroup = async () => {
		setIsCreatingGroup(true);
		setCreateGroupError("");

		if (!formData.group_name.trim()) {
			setCreateGroupError("Group name is required");
			setIsCreatingGroup(false);

			return;
		}

		if (!formData.invite_code.trim()) {
			setCreateGroupError("Invite code is required");
			setIsCreatingGroup(false);

			return;
		}

		try {
			const apiData = {
				editor_id: user?.id || user?.email || "unknown",
				group_name: formData.group_name.trim(),
				group_type: "author-group",
				shared_passcode: formData.invite_code.trim(),
			};

			const response = await fetch("/api/groups", {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
				body: JSON.stringify(apiData),
			});

			if (response.ok) {
				// Set success message
				setCreateGroupSuccess(
					`Group "${formData.group_name.trim()}" created successfully!`,
				);

				// Reset form
				setFormData({
					group_name: "",
					invite_code: "",
				});

				// Close modal after a short delay to show success message
				setTimeout(() => {
					onOpenChange();
					setCreateGroupSuccess("");
				}, 1500);

				// Refresh group data from the server
				if (session.data?.user?.id) {
					try {
						const updatedGroups = await getGroupMembershipsByEditorAndGroupType(session.data.user.id, "");
						const updatedGroup = updatedGroups[0];
						// biome-ignore lint/suspicious/noExplicitAny: <explanation>
						setUserGroup(updatedGroup || {});

						// If group exists and has a shared passcode, fetch its members
						if (updatedGroup && updatedGroup.shared_passcode) {
							fetchGroupMembers(updatedGroup.shared_passcode);
						}
					} catch (error) {
						console.error("Error refreshing group data:", error);
					}
				}
			} else {
				const data = await response.json();

				setCreateGroupError(
					data.error || data.message || "Failed to create group",
				);
			}
		} catch (error) {
			console.error("Error creating group:", error);
			setCreateGroupError("An unexpected error occurred. Please try again.");
		} finally {
			setIsCreatingGroup(false);
		}
	};

	return (
		<ContentWrapperBase
			className="border-none"
			title={t(`${groupType}-memberships`)}
		>
			<div className="p-6 ">
				<div className="space-y-8">
					{isEditor && (
						<section className="p-6 border rounded-lg bg-gray-50">
							<h2 className="mb-4 text-xl font-semibold">
								{hasGroup ? "Your Group" : "Create Your Group"}
							</h2>

							{!hasGroup ? (
								<div className="space-y-4">
									<p className="text-default-500">
										As an editor, you can create and manage your own group.
										Create a group to invite team members and manage content
										collaboratively.
									</p>
									<Button
										className="text-white"
										color="primary"
										onPress={() => {
											onOpen();
											setCreateGroupError("");
											setCreateGroupSuccess("");
										}}
									>
										Create New Group
									</Button>
								</div>
							) : (
								<div className="space-y-4">
									<div className="grid grid-cols-1 gap-4 md:grid-cols-2">
										<div>
											<h3 className="text-lg font-semibold">
												{ userGroup?.group_name}
											</h3>
											<p className="text-default-500">
												Type: {userGroup?.group_type}
											</p>
										</div>
										<div>
											<label
												className="block mb-2 text-sm font-medium"
												htmlFor="group-code-input"
											>
												Group Code
											</label>
											<div className="flex gap-2">
												<Input
													readOnly
													className="flex-1 text-black"
													id="group-code-input"
													value={userGroup?.shared_passcode}
												/>
												<Button
													className="text-white"
													color="primary"
													onPress={handleCopyGroupCode}
												>
													<span className="flex items-center gap-2">
														{/* biome-ignore lint/a11y/noSvgWithoutTitle: <explanation> */}
														<svg
															fill="none"
															height="20"
															viewBox="0 0 24 24"
															width="20"
															xmlns="http://www.w3.org/2000/svg"
														>
															<path
																d="M8 8H6C4.89543 8 4 8.89543 4 10V18C4 19.1046 4.89543 20 6 20H14C15.1046 20 16 19.1046 16 18V16M8 8V6C8 4.89543 8.89543 4 10 4H18C19.1046 4 20 4.89543 20 6V14C20 15.1046 19.1046 16 18 16H16M8 8H14C15.1046 8 16 8.89543 16 10V16"
																stroke="currentColor"
																strokeLinecap="round"
																strokeLinejoin="round"
																strokeWidth="2"
															/>
														</svg>
														Copy Code
													</span>
												</Button>
											</div>
											<p className="mt-1 text-xs text-default-500">
												Share this code with team members to join your group
											</p>
										</div>
									</div>
								</div>
							)}
						</section>
					)}
					<section>
						<h2 className="mb-2 text-xl font-semibold">
							{t("invite-by-link")}
						</h2>
						<p className="mb-4 text-default-500">
							{t("share-link-with-members")}
						</p>
						<div className="flex gap-2">
							<Input
								readOnly
								className="flex-1"
								value={generatedInvitationLink}
							/>
							<Button
								className="text-white"
								color="primary"
								onPress={handleCopyLink}
							>
								<span className="flex items-center gap-2">
									{/* biome-ignore lint/a11y/noSvgWithoutTitle: <explanation> */}
									<svg
										fill="none"
										height="20"
										viewBox="0 0 24 24"
										width="20"
										xmlns="http://www.w3.org/2000/svg"
									>
										<path
											d="M8 8H6C4.89543 8 4 8.89543 4 10V18C4 19.1046 4.89543 20 6 20H14C15.1046 20 16 19.1046 16 18V16M8 8V6C8 4.89543 8.89543 4 10 4H18C19.1046 4 20 4.89543 20 6V14C20 15.1046 19.1046 16 18 16H16M8 8H14C15.1046 8 16 8.89543 16 10V16"
											stroke="currentColor"
											strokeLinecap="round"
											strokeLinejoin="round"
											strokeWidth="2"
										/>
									</svg>
									{t("copy-link")}
								</span>
							</Button>
						</div>
					</section>

					{/* Invite by email section */}
					<section>
						<h2 className="mb-2 text-xl font-semibold">
							{t("invite-by-email")}
						</h2>
						<p className="mb-4 text-default-500">
							{t("email-invite-validity")}
						</p>
						<div className="flex gap-2">
							<Input
								className="flex-1"
								placeholder={t("enter-email-placeholder")}
								type="email"
								value={inviteEmail}
								onChange={(e) => setInviteEmail(e.target.value)}
							/>
							<Button
								className="text-white"
								color="primary"
								onPress={handleInvite}
							>
								{t("invite")}
							</Button>
						</div>
						<p className="mt-1 text-sm text-default-500">
							{t("send-invitation-one-at-time")}
						</p>
					</section>

					{/* Group memberships list */}
					<section className="border rounded-lg">
						<div className="flex items-center justify-between p-4 border-b">
							<div className="flex items-center gap-2">
								<Checkbox />
								<h3 className="text-lg font-semibold">
									{t(`team-memberships`)}
								</h3>
							</div>
							<Dropdown>
								<DropdownTrigger>
									<Button className="min-w-unit-24" variant="light">
										{t("actions")}
										{/* biome-ignore lint/a11y/noSvgWithoutTitle: <explanation> */}
										<svg
											fill="none"
											height="16"
											viewBox="0 0 24 24"
											width="16"
											xmlns="http://www.w3.org/2000/svg"
										>
											<path
												d="M6 9L12 15L18 9"
												stroke="currentColor"
												strokeLinecap="round"
												strokeLinejoin="round"
												strokeWidth="2"
											/>
										</svg>
									</Button>
								</DropdownTrigger>
								<DropdownMenu aria-label="Membership actions">
									<DropdownItem key={"action-remove"}>
										{t("remove")}
									</DropdownItem>
									<DropdownItem key={"action-approve"}>
										{t("approve")}
									</DropdownItem>
								</DropdownMenu>
							</Dropdown>
						</div>

						<div className="divide-y">
							{isLoadingMembers ? (
								<div className="p-4 text-center text-default-500">
									Loading members...
								</div>
							) : groupMembers.length > 0 ? (
								groupMembers.map((membership, idx) => (
									<div
										key={`group-${membership?.id}-${idx}`}
										className="flex items-center justify-between p-4"
									>
										<div className="flex items-center gap-4">
											<Checkbox isDisabled={membership.userId === user?.id} />
											<span>{membership.userId}</span>
										</div>

										<span
											className={`px-3 py-1 rounded-full text-sm ${
												membership.approved
													? "bg-green-100 text-green-700"
													: "bg-yellow-100 text-yellow-700"
											}`}
										>
											{membership.approved ? t("approved") : t("pending")}
										</span>
									</div>
								))
							) : (
								<div className="p-4 text-center text-default-500">
									No members found
								</div>
							)}
						</div>
					</section>
				</div>
			</div>

			<Modal isOpen={isOpen} size="2xl" onOpenChange={onOpenChange}>
				<ModalContent>
					{(onClose) => (
						<>
							<ModalHeader className="flex flex-col gap-1">
								Create New Group
							</ModalHeader>
							<ModalBody>
								{createGroupError && (
									<div className="p-4 text-red-700 bg-red-100 border border-red-300 rounded-md">
										{createGroupError}
									</div>
								)}

								{createGroupSuccess && (
									<div className="p-4 text-green-700 bg-green-100 border border-green-300 rounded-md">
										{createGroupSuccess}
									</div>
								)}

								<div className="space-y-4">
									<Input
										isRequired
										label="Group Name"
										placeholder="Enter group name"
										value={formData.group_name}
										variant="bordered"
										onChange={(e) =>
											handleInputChange("group_name", e.target.value)
										}
									/>

									<div className="space-y-2">
										<Input
											isRequired
											endContent={
												<Button
													className="text-xs"
													size="sm"
													variant="flat"
													onPress={generateInviteCode}
												>
													Generate
												</Button>
											}
											label="Invite Code"
											placeholder="Enter or generate invite code"
											value={formData.invite_code}
											variant="bordered"
											onChange={(e) =>
												handleInputChange("invite_code", e.target.value)
											}
										/>
										<p className="text-xs text-gray-500">
											This code will be used by authors to join your group
										</p>
									</div>
								</div>
							</ModalBody>
							<ModalFooter>
								<Button variant="bordered" onPress={onClose}>
									Cancel
								</Button>
								<Button
									className="text-white"
									color="primary"
									isLoading={isCreatingGroup}
									onPress={handleCreateGroup}
								>
									{isCreatingGroup ? "Creating..." : "Create Group"}
								</Button>
							</ModalFooter>
						</>
					)}
				</ModalContent>
			</Modal>
		</ContentWrapperBase>
	);
}

export default GroupMemberships;
