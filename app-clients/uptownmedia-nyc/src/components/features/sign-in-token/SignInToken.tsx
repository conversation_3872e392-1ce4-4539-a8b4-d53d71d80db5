"use client";

import type { Session } from "next-auth";

import { But<PERSON> } from "@heroui/button";
import { useSession } from "next-auth/react";
import { signOut } from "next-auth/react";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";

import LoadingContent from "@/components/ui/LoadingContent";
const getRedirectPathByRole = (user: Session["user"]): string => {
	if (user.isSuperuser) return "/portal/super-user";
	if (user.isAdmin) return "/portal/admin";
	if (user.isEditor) return "/portal/editor";
	if (user.isAuthor) return "portal/author";
	if (user.isGuess) return "/";

	return "/";
};

export default function SignTokenLink({
	token,
}: {
	roleName: string;
	token: string;
}) {
	const router = useRouter();
	const { data: session, update } = useSession();
	const [isCodeSent, setIsCodeSent] = useState(false);
	const [verificationCode, setVerificationCode] = useState("");
	const [isVerifying, setIsVerifying] = useState(false);
	const [error, setError] = useState("");
	const [loading, setLoading] = useState(true);

	const [isValid, setIsValid] = useState<boolean | null>(null);

	const handlerRole = (data: Session | null) => {
		const result: Record<string, boolean> = {};

		if (data?.user?.isAdmin) {
			result.adminMfaVerified = true;
		}
		if (data?.user?.isEditor) {
			result.editorMfaVerified = true;
		}

		if (data?.user?.isAuthor) {
			result.authorMfaVerified = true;
		}
		if (data?.user?.isGuess) {
			result.guessMfaVerified = true;
		}
		if (data?.user?.isSuperuser) {
			result.superuserMfaVerified = true;
		}

		return Object.keys(result).length > 0 ? result : undefined;
	};
	const handlerupdate = async () => {
		await update(handlerRole(session));
	};

	useEffect(() => {
		const validate = async () => {
			setLoading(true);
			const response = await fetch(`/api/auth/signing-token-link/${token}`, {
				method: "GET",
				headers: {
					"Content-Type": "application/json",
				},
			});

			const data = await response.json();

			setIsValid(data.isValid);
			setLoading(false);
		};

		validate();
	}, [token]);

	if (loading) {
		return <LoadingContent />;
	}
	if (!session) {
		return (
			<>
				<h1>Unauthorize</h1>
			</>
		);
	}

	if (!isValid) {
		return (
			<div className="flex flex-col items-center justify-center min-h-screen p-4">
				<div className="w-full max-w-md p-6 bg-white rounded-lg shadow-md">
					<h1 className="text-xl font-bold text-center text-red-600">
						Invalid Token
					</h1>
					<p className="mt-4 text-center">
						The verification token is invalid or has expired.
					</p>
					<div className="mt-6 text-center">
						<Button
							onPress={() => {
								signOut({ callbackUrl: "/auth/sign-in" });
								router.push("/auth/sign-in");
							}}
						>
							Return to Sign In
						</Button>
					</div>
				</div>
			</div>
		);
	}

	const handleSendCode = async () => {
		try {
			const response = await fetch("/api/auth/verification-code/send", {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
				body: JSON.stringify({
					userId: session?.user?.id,
				}),
			});

			const data = await response.json();

			if (data.success) {
				setIsCodeSent(true);
				setError("");
			} else {
				setError(data.error || "Failed to send verification code");
			}
		} catch (err) {
			console.error("Error sending verification code:", err);
			setError("An error occurred while sending the verification code");
		}
	};
	const handlerTokenLink = async () => {
		await fetch(`/api/auth/signing-token-link/${token}`, {
			method: "PUT",
			headers: {
				"Content-Type": "application/json",
			},
		});
	};

	const handleVerifyCode = async () => {
		setIsVerifying(true);
		setError("");

		try {
			const response = await fetch("/api/auth/verification-code", {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
				body: JSON.stringify({
					verificationCode: verificationCode,
				}),
			});

			const data = await response.json();

			if (data.success) {
				await handlerupdate();
				await handlerTokenLink();

				if (!session?.user?.hasPassword) {
					router.push("/auth/set-password");

					return;
				}

				// biome-ignore lint/style/noNonNullAssertion: <explanation>
				const pathRedirect = getRedirectPathByRole(session?.user!);

				if (data.redirectPath) {
					const redirectUrl = new URL(data.redirectPath);

					router.push(redirectUrl.pathname + redirectUrl.search);
				} else {
					router.push(pathRedirect);
				}
			} else {
				setError(data.error || "Invalid verification code. Please try again.");
			}
		} catch (err) {
			console.error("Error verifying code:", err);
			setError("An error occurred while verifying the code");
		} finally {
			setIsVerifying(false);
		}
	};

	return (
		<div className="flex flex-col items-center justify-center min-h-screen p-4">
			<div className="w-full max-w-md p-6 bg-white rounded-lg shadow-md">
				<h1 className="text-xl font-bold text-center">Sign In </h1>

				<div className="mt-6 text-center">
					<p className="font-medium">Verify with your email</p>
					<p className="mt-1 text-sm">{session?.user.name}</p>

					<p className="mt-4 text-sm">
						Send code via Email to {session?.user.email}
					</p>

					<p className="mt-2 text-xs text-gray-500">
						Carrier messaging charges may apply
					</p>
				</div>

				{!isCodeSent ? (
					<div className="mt-6">
						<Button fullWidth variant="bordered" onPress={handleSendCode}>
							Receive a code via Email
						</Button>
					</div>
				) : (
					<div className="mt-6">
						<div className="mb-4">
							<label
								className="block mb-2 text-sm font-medium"
								htmlFor="verification-code"
							>
								Enter verification code
							</label>
							<input
								className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
								id="verification-code"
								maxLength={6}
								placeholder="6-digit code"
								type="text"
								value={verificationCode}
								onChange={(e) => setVerificationCode(e.target.value)}
							/>
							{error && <p className="mt-2 text-sm text-red-600">{error}</p>}
						</div>

						<Button
							fullWidth
							color="primary"
							isDisabled={verificationCode.length !== 6}
							isLoading={isVerifying}
							onPress={handleVerifyCode}
						>
							Verify Code
						</Button>

						<div className="mt-4 text-center">
							<button
								className="text-sm text-blue-600 hover:underline"
								disabled={isVerifying}
								type="button"
								onClick={handleSendCode}
							>
								Didn't receive a code? Send again
							</button>
						</div>
					</div>
				)}
			</div>
		</div>
	);
}
