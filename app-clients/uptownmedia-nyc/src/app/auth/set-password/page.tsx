"use client";

import type React from "react";

import { <PERSON><PERSON>, Card, CardBody, Input } from "@heroui/react";
import { Icon } from "@iconify/react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { useState } from "react";

import { updatePasswordAction } from "./updatePasswordAction";

import { title } from "@/components/primitives";

export default function AddPasswordForm() {
	const { data: session } = useSession();
	const [password, setPassword] = useState("");
	const [confirmPassword, setConfirmPassword] = useState("");
	const [showPassword, setShowPassword] = useState(false);
	const [showConfirmPassword, setShowConfirmPassword] = useState(false);
	const [isLoading, setIsLoading] = useState(false);
	const [error, setError] = useState("");
	const [success, setSuccess] = useState(false);

	const router = useRouter();
	const validatePassword = (password: string) => {
		const minLength = password.length >= 8;
		const hasUpperCase = /[A-Z]/.test(password);
		const hasLowerCase = /[a-z]/.test(password);
		const hasNumbers = /\d/.test(password);
		const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(password);

		return {
			minLength,
			hasUpperCase,
			hasLowerCase,
			hasNumbers,
			hasSpecialChar,
			isValid:
				minLength &&
				hasUpperCase &&
				hasLowerCase &&
				hasNumbers &&
				hasSpecialChar,
		};
	};

	const passwordValidation = validatePassword(password);
	const passwordsMatch = password === confirmPassword && confirmPassword !== "";

	const handleSubmit = async (e: React.FormEvent) => {
		e.preventDefault();
		setError("");

		if (!passwordValidation.isValid) {
			setError("Password doesn't meet security requirements");

			return;
		}

		if (!passwordsMatch) {
			setError("Passwords don't match");

			return;
		}

		setIsLoading(true);

		try {
			await updatePasswordAction(password, session?.user.id || "");

			setSuccess(true);
			setPassword("");
			setConfirmPassword("");
		} catch (err) {
			setError("Error setting password. Please try again.");
		} finally {
			setIsLoading(false);
		}
	};

	if (success) {
		return (
			<div className="flex items-center justify-center w-full min-h-screen p-4">
				<Card className="w-full max-w-[545px] shadow-none sm:shadow-large transition-shadow duration-500 ease-in-out mx-4">
					<CardBody className="gap-6 p-14 text-center">
						<div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-green-100">
							<Icon
								className="text-3xl text-green-600"
								icon="heroicons:shield-check-20-solid"
							/>
						</div>
						<h1
							className={title({
								size: "lg",
								className: "text-center text-green-600",
							})}
						>
							Password Set!
						</h1>
						<p className="text-sm font-semibold text-center text-black">
							Your password has been successfully configured. <br />
							You can now use both Google and your email and password to sign
							in.
						</p>
						<Button
							className="w-full"
							variant="bordered"
							onPress={() => {
								router.push("/auth/sign-in");
								setSuccess(false);

								return;
							}}
						>
							Close
						</Button>
					</CardBody>
				</Card>
			</div>
		);
	}

	return (
		<div className="flex items-center justify-center w-full min-h-screen p-4">
			<Card className="w-full max-w-[545px] shadow-none sm:shadow-large transition-shadow duration-500 ease-in-out mx-4">
				<CardBody className="gap-6 p-14">
					<div className="text-center">
						<div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-blue-100">
							<Icon
								className="text-3xl text-blue-600"
								icon="heroicons:lock-closed-20-solid"
							/>
						</div>
						<h1 className={title({ size: "lg", className: "text-center" })}>
							Set Your Password
						</h1>
						<p className="text-sm font-semibold text-center text-black mt-4">
							For added security, set a password for your account. <br />
							This will allow you to sign in with email and password in addition
							to Google.
						</p>
					</div>

					{error && (
						<div className="p-3 rounded-lg bg-red-50 border border-red-200">
							<p className="text-sm text-red-600 text-center">{error}</p>
						</div>
					)}

					<form className="space-y-10" onSubmit={handleSubmit}>
						<Input
							className="mt-2"
							endContent={
								<Button
									isIconOnly
									variant="light"
									onPress={() => setShowPassword(!showPassword)}
								>
									<Icon
										className="text-xl"
										icon={
											showPassword
												? "heroicons:eye-slash-20-solid"
												: "heroicons:eye-20-solid"
										}
									/>
								</Button>
							}
							label="New Password"
							labelPlacement="outside"
							placeholder="Enter your new password"
							type={showPassword ? "text" : "password"}
							value={password}
							onChange={(e) => setPassword(e.target.value)}
						/>

						<Input
							className="mt-2"
							endContent={
								<Button
									isIconOnly
									variant="light"
									onPress={() => setShowConfirmPassword(!showConfirmPassword)}
								>
									<Icon
										className="text-xl"
										icon={
											showConfirmPassword
												? "heroicons:eye-slash-20-solid"
												: "heroicons:eye-20-solid"
										}
									/>
								</Button>
							}
							label="Confirm Password"
							labelPlacement="outside"
							placeholder="Confirm your new password"
							type={showConfirmPassword ? "text" : "password"}
							value={confirmPassword}
							onChange={(e) => setConfirmPassword(e.target.value)}
						/>

						{password && (
							<div className="space-y-3">
								<p className="text-sm font-medium text-black">
									Password Requirements:
								</p>
								<div className="space-y-2">
									<div
										className={`flex items-center gap-2 text-sm ${passwordValidation.minLength ? "text-green-600" : "text-gray-500"}`}
									>
										<Icon
											className="text-lg"
											icon={
												passwordValidation.minLength
													? "heroicons:check-circle-20-solid"
													: "heroicons:minus-circle-20-solid"
											}
										/>
										Minimum 8 characters
									</div>
									<div
										className={`flex items-center gap-2 text-sm ${passwordValidation.hasUpperCase ? "text-green-600" : "text-gray-500"}`}
									>
										<Icon
											className="text-lg"
											icon={
												passwordValidation.hasUpperCase
													? "heroicons:check-circle-20-solid"
													: "heroicons:minus-circle-20-solid"
											}
										/>
										One uppercase letter
									</div>
									<div
										className={`flex items-center gap-2 text-sm ${passwordValidation.hasLowerCase ? "text-green-600" : "text-gray-500"}`}
									>
										<Icon
											className="text-lg"
											icon={
												passwordValidation.hasLowerCase
													? "heroicons:check-circle-20-solid"
													: "heroicons:minus-circle-20-solid"
											}
										/>
										One lowercase letter
									</div>
									<div
										className={`flex items-center gap-2 text-sm ${passwordValidation.hasNumbers ? "text-green-600" : "text-gray-500"}`}
									>
										<Icon
											className="text-lg"
											icon={
												passwordValidation.hasNumbers
													? "heroicons:check-circle-20-solid"
													: "heroicons:minus-circle-20-solid"
											}
										/>
										One number
									</div>
									<div
										className={`flex items-center gap-2 text-sm ${passwordValidation.hasSpecialChar ? "text-green-600" : "text-gray-500"}`}
									>
										<Icon
											className="text-lg"
											icon={
												passwordValidation.hasSpecialChar
													? "heroicons:check-circle-20-solid"
													: "heroicons:minus-circle-20-solid"
											}
										/>
										One special character
									</div>
								</div>
							</div>
						)}

						{confirmPassword && (
							<div
								className={`flex items-center gap-2 text-sm ${passwordsMatch ? "text-green-600" : "text-red-500"}`}
							>
								<Icon
									className="text-lg"
									icon={
										passwordsMatch
											? "heroicons:check-circle-20-solid"
											: "heroicons:x-circle-20-solid"
									}
								/>
								{passwordsMatch ? "Passwords match" : "Passwords don't match"}
							</div>
						)}

						<div className="flex gap-3 pt-4">
							<Button
								className="flex-1"
								variant="bordered"
								onPress={() => {
									setPassword("");
									setConfirmPassword("");
									setError("");
								}}
							>
								Cancel
							</Button>
							<Button
								className="flex-1 text-white bg-black"
								isDisabled={!passwordValidation.isValid || !passwordsMatch}
								isLoading={isLoading}
								type="submit"
							>
								{isLoading ? "Setting..." : "Set Password"}
							</Button>
						</div>
					</form>
				</CardBody>
			</Card>
		</div>
	);
}
