"use client";
import { But<PERSON> } from "@heroui/react";
import { useSession } from "next-auth/react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { use } from "react";

export default function ArticlesPage({
	params,
}: {
	params: Promise<{ role: "admin" | "author" | "editor" | "guest" }>;
}) {
	const { role } = use(params);
	const { status } = useSession();
	const router = useRouter();
	const [articles, setArticles] = useState([]);
	const [isLoading, setIsLoading] = useState(true);

	useEffect(() => {
		const fetchArticles = async () => {
			try {
				const response = await fetch("/api/articles");

				if (response.ok) {
					const data = await response.json();

					setArticles(data.articles || []);
				}
			} catch (error) {
				console.error("Error fetching articles:", error);
			} finally {
				setIsLoading(false);
			}
		};

		fetchArticles();
	}, []);

	if (status === "loading") {
		return <div className="p-8 text-center">Loading...</div>;
	}

	if (status === "unauthenticated") {
		router.push("/auth/sign-in");

		return null;
	}

	return (
		<div className="max-w-4xl p-6 mx-auto">
			<div className="flex items-center justify-between mb-8">
				<h1 className="text-2xl font-bold">Articles</h1>
				{(role === "editor" || role === "admin") && (
					<Link href={`/portal/${role}/articles/create`}>
						<Button color="primary">Create New Article</Button>
					</Link>
				)}
			</div>

			{isLoading ? (
				<div className="p-4 text-center">Loading articles...</div>
			) : articles.length > 0 ? (
				<div className="space-y-4">
					{articles.map(
						(article: {
							id: string;
							title: string;
							description: string;
							topic: string;
							published: string;
						}) => (
							<div
								key={article.id}
								className="p-4 transition-shadow border rounded-md shadow-sm hover:shadow-md"
							>
								<h2 className="text-xl font-semibold">{article.title}</h2>
								<p className="text-gray-600 line-clamp-2">
									{article.description}
								</p>
								<div className="flex items-center justify-between mt-2">
									<span className="text-sm text-gray-500">
										Topic: {article.topic}
									</span>
									<span className="text-sm text-gray-500">
										Published:{" "}
										{new Date(article.published).toLocaleDateString()}
									</span>
								</div>
							</div>
						),
					)}
				</div>
			) : (
				<div className="p-8 text-center text-gray-500">
					No articles found.{" "}
					{(role === "editor" || role === "admin") &&
						"Create your first article!"}
				</div>
			)}
		</div>
	);
}
