import { z } from "zod";

import { UserServices } from "../services/userServices";

import { AppError } from "@/app/api/shared/errors";

export async function PUT(request: Request) {
	try {
		const userServices = new UserServices();
		const body = await request.json();

		await userServices.changeUserPassword(body.password, body.userId);

		return Response.json(
			{ msg: "Password Changed", status: 200 },
			{ status: 200 },
		);
	} catch (error) {
		if (error instanceof z.ZodError) {
			return Response.json({ ...error.errors }, { status: 400 });
		}
		if (error instanceof AppError) {
			return Response.json(
				{ error: error.message },
				{ status: error.statusCode },
			);
		}

		return Response.json({ message: "Internal Server Error" }, { status: 500 });
	}
}
