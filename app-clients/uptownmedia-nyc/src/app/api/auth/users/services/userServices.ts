import type { UserRequest } from "../dto/userRequest";

import bcrypt from "bcryptjs";
import { uuidv7 } from "uuidv7";

import { User } from "../dao/user";
import { UserDynamoDBRepository } from "../repository/userRepository";

import { ConflictError, NotFoundError } from "@/app/api/shared/errors";

function getRandomInt(min: number, max: number): number {
	const range = max - min;
	const randomBuffer = new Uint32Array(1);

	crypto.getRandomValues(randomBuffer);

	return min + (randomBuffer[0] % range);
}
export class UserServices {
	private userRepository: UserDynamoDBRepository;

	constructor() {
		this.userRepository = new UserDynamoDBRepository();
	}

	public async createUser(
		userRequest: UserRequest,
		roleId: string,
		socialProvider?: string,
		// biome-ignore lint/suspicious/noExplicitAny: <explanation>
		attributes?: Record<string, any>,
	): Promise<User> {
		const existingUser = await this.userRepository.getUserByEmail(
			userRequest.email,
		);

		if (existingUser) {
			throw new ConflictError("User already exists");
		}

		const password = uuidv7();
		const hashedPassword = await bcrypt.hash(password, 10);
		const userId = uuidv7();
		const tenantId = "1";

		const user = new User(
			userId,
			tenantId,
			userRequest.name,
			"",
			userRequest.email,
			[roleId],
			socialProvider ?? "",
			0,
			attributes,
			userRequest.phone,
		);

		await this.userRepository.saveUser(user);

		return user;
	}

	public async getUsers() {
		const listOfUsers = await this.userRepository.getAllUsers("1");

		return listOfUsers;
	}
	public async getUserById(id: string) {
		const user = await this.userRepository.getUserById(id);

		return user;
	}
	public async getUserByEmail(email: string) {
		const user = await this.userRepository.getUserByEmail(email);

		return user;
	}
	public async getUserByEditorCode(code: string) {
		const user = await this.userRepository.getUsersByEditorCode(code);

		return user;
	}
	public async authenticateUser(email: string, password: string) {
		const user = await this.userRepository.getUserByEmail(email);

		if (!user) {
			throw new NotFoundError("User not found");
		}
		console.log(user);
		console.log(password);

		const isPasswordValid = await bcrypt.compare(password, user.getPassword());

		console.log("isPasswordValid", isPasswordValid);

		if (!isPasswordValid) {
			throw new ConflictError("Invalid password");
		}

		return user;
	}
	public async changeUserPassword(newPassword: string, userId: string) {
		const user = await this.userRepository.getUserById(userId);

		if (!user) {
			throw new NotFoundError("User not found");
		}
		const hashedPassword = await bcrypt.hash(newPassword, 10);

		user.setPassword(hashedPassword);
		await this.userRepository.saveUser(user);
	}
	public async getUserByRole(roleId: string): Promise<string[]> {
		const users = await this.userRepository.getUsersByRole(roleId, "1");

		return users.map((user) => {
			return user.getName();
		});
	}
	public async getUserAdmin(roleId: string): Promise<User[]> {
		const users = await this.userRepository.getUsersByRole(roleId, "1");

		return users;
	}

	// public async updateUserCodeToAuthor(code: string, email: string) {
	//   const user = await this.userRepository.getUserByEmail(email);
	//
	//   if (!user) {
	//     throw new NotFoundError("User not found");
	//   }
	//   user.setCodeUsed(code);
	//   await this.userRepository.saveUser(user);
	// }
	// public async addEditorCodeToUser(id: string) {
	//   const user = await this.userRepository.getUserById(id);
	//
	//   if (!user) {
	//     throw new NotFoundError("User not found");
	//   }
	//   if (user.getRole() !== UserRole.EDITOR) {
	//     throw new ConflictError("User is not an editor");
	//   }
	//
	//   let code = `${getRandomInt(0, 10000)}-${randomLetters(4)}-${randomLetters(3)}`;
	//   let existingUsers = await this.userRepository.getUsersByEditorCode(code);
	//
	//   do {
	//     code = `${getRandomInt(0, 10000)}-${randomLetters(4)}-${randomLetters(3)}`;
	//     existingUsers = await this.userRepository.getUsersByEditorCode(code);
	//   } while (existingUsers.length > 0);
	//
	//   user.setEditorCode(code);
	//   await this.userRepository.saveUser(user);
	// }
	// public async changeUserPassword(id: string, password: string) {
	//   const user = await this.userRepository.getUserById(id);
	//
	//   if (!user) {
	//     throw new NotFoundError("User not found");
	//   }
	//
	//   const hashedPassword = await bcrypt.hash(password, 10);
	//
	//   user.setPassword(hashedPassword);
	//   await this.userRepository.saveUser(user);
	// }

	public async updateUser(
		userId: string,
		userRequest: UserRequest,
		socialProvider?: string,
	) {
		const user = await this.userRepository.getUserById(userId);

		if (!user) {
			throw new NotFoundError("User not found");
		}
		user.setName(userRequest.name);
		if (userRequest.phone) {
			user.setPhone(userRequest.phone);
		}
		if (socialProvider) {
			user.setSocialProvider(socialProvider);
		}
		await this.userRepository.saveUser(user);
	}
	public deleteUser() {}
	public async addFailedAttempt(userId: string) {
		const user = await this.userRepository.getUserById(userId);

		if (!user) {
			throw new NotFoundError("User not found");
		}
		const failedAttemptCount = user.getFailedAttemptCount();

		console.log("failedAttemptCount", failedAttemptCount);

		user.setFailedAttemptCount(failedAttemptCount + 1);
		await this.userRepository.saveUser(user);
	}
	public async resetFailedAttempt(userId: string) {
		const user = await this.userRepository.getUserById(userId);

		if (!user) {
			throw new NotFoundError("User not found");
		}

		user.setFailedAttemptCount(0);
		await this.userRepository.saveUser(user);
	}
}

function randomLetters(length: number): string {
	const letters = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";

	return Array.from({ length }, () =>
		letters.charAt(getRandomInt(0, letters.length)),
	).join("");
}
