import {
	DeleteCommand,
	type DeleteCommandInput,
	PutCommand,
	type PutCommandInput,
	type ScanCommandInput,
	UpdateCommand,
	type UpdateCommandInput,
} from "@aws-sdk/lib-dynamodb";

import { BaseDynamoDBRepository } from "../../../shared/dynamoDbRepository";
import { User, type UserRole } from "../dao/user";

import { configDbEnv } from "@/utils/configDbEnv";

/**
 * Repository to interact with the Users table in DynamoDB
 */
export class UserDynamoDBRepository extends BaseDynamoDBRepository {
	/**
	 * Constructor to set the tableName for UserDynamoDBRepository
	 */
	constructor() {
		super(configDbEnv.authUserTable);
	}

	/**
	 * Convert a DynamoDB object to a User instance
	 * @param item Object received from DynamoDB
	 * @returns Instance of User
	 */

	// biome-ignore lint/suspicious/noExplicitAny: <explanation>
	private mapToUser(item: any): User {
		const user = new User(
			item.user_id,
			item.tenant_id,
			item.name,
			item.password,
			item.email,
			item.roles_ids,
			item.social_provider,
			Number(item.failed_attempt_count),
			item.phone_number,
			item.attributes,
			item.lockout_expiresAt,
		);

		return user;
	}

	/**
	 * Convert a User instance to an object for saving in DynamoDB
	 * @param user User instance
	 * @returns Object ready to save in DynamoDB
	 */

	// biome-ignore lint/suspicious/noExplicitAny: <explanation>
	private mapToDynamoDBItem(user: User): Record<string, any> {
		return {
			user_id: user.getUserId(),
			tenant_id: user.getTenantId(),
			name: user.getName(),
			password: user.getPassword(),
			email: user.getEmail(),
			roles_ids: user.getRolesIds(),
			failed_attempt_count: user.getFailedAttemptCount(),
			social_provider: user.getSocialProvider(),
			phoneNumber: user.getPhone(),
		};
	}

	/**
	 * Create or update a user in DynamoDB
	 * @param user User instance to save
	 * @returns Promise with the result of the operation
	 */
	async saveUser(user: User): Promise<void> {
		try {
			const item = this.mapToDynamoDBItem(user);

			const params: PutCommandInput = {
				TableName: this.tableName,
				Item: item,
			};

			const command = new PutCommand(params);

			await this.docClient.send(command);
		} catch (error) {
			console.error(`Error saving user with id ${user.getUserId()}:`, error);
			throw error;
		}
	}

	/**
	 * Get a user by ID
	 * @param userId User ID
	 * @returns Promise with the found user or null
	 */
	async getUserById(userId: string): Promise<User | null> {
		try {
			const item = await this.getItemById([
				["user_id", userId],
				["tenant_id", "1"],
			]);

			if (!item) {
				return null;
			}

			return this.mapToUser(item);
		} catch (error) {
			console.error(`Error fetching user with id ${userId}:`, error);
			throw error;
		}
	}

	/**
	 * Get a user by email
	 * @param email User email
	 * @returns Promise with the found user or null
	 */
	async getUserByEmail(email: string): Promise<User | null> {
		try {
			// Use the scanWithFilter method from the base class
			const items = await this.scanWithFilter("email = :email", {
				":email": email,
			});

			if (items.length === 0) {
				return null;
			}

			return this.mapToUser(items[0]);
		} catch (error) {
			console.error(`Error fetching user with email ${email}:`, error);
			throw error;
		}
	}

	/**
	 * Get all users
	 * @returns Promise with the list of all users
	 */
	async getAllUsers(tenantId: string): Promise<Partial<User>[]> {
		try {
			const items = await this.scanWithFilter("tenant_id = :tenantId", {
				":tenantId": tenantId,
			});

			return items.map((item) => {
				const user = this.mapToUser(item);

				return user.toSafeObject();
			});
		} catch (error) {
			console.error("Error fetching all users:", error);
			throw error;
		}
	}

	/**
	 * Get users by role
	 * @param role User role to filter
	 * @returns Promise with the list of filtered users
	 */
	async getUsersByRole(roleId: string, tenantId: string): Promise<User[]> {
		try {
			const filterExpression =
				"tenant_id = :tenant_id AND contains(roles_ids, :roles_ids)";

			const expressionAttributeValues = {
				":tenant_id": tenantId,
				":roles_ids": roleId,
			};

			const params: ScanCommandInput = {
				TableName: this.tableName,
				FilterExpression: filterExpression,
				ExpressionAttributeValues: expressionAttributeValues,
			};

			const items = await this.fetchAllItems(params);

			return items.map((item) => {
				const user = this.mapToUser(item);

				return user;
			});
		} catch (error) {
			console.error(
				`Error scanning table ${this.tableName} with filters:`,
				error,
			);
			throw error;
		}
	}
	async getUsersByEditorCode(editorCode: string): Promise<User[]> {
		try {
			const items = await this.scanWithFilter("editorCode = :editorCode", {
				":editorCode": editorCode,
			});

			return items.map((item) => this.mapToUser(item));
		} catch (error) {
			console.error("Error fetching users with editorCode");
			throw error;
		}
	}

	/**
	 * Get active or inactive users
	 * @param isActive Activation status to filter
	 * @returns Promise with the list of filtered users
	 */
	async getUsersByActiveStatus(isActive: boolean): Promise<User[]> {
		try {
			const items = await this.scanWithFilter("isActive = :isActive", {
				":isActive": isActive,
			});

			return items.map((item) => this.mapToUser(item));
		} catch (error) {
			console.error(
				`Error fetching users with active status ${isActive}:`,
				error,
			);
			throw error;
		}
	}

	/**
	 * Delete a user by ID
	 * @param userId ID of the user to delete
	 * @returns Promise with the result of the operation
	 */
	async deleteUser(userId: string): Promise<void> {
		try {
			const params: DeleteCommandInput = {
				TableName: this.tableName,
				Key: {
					id: userId,
				},
			};

			const command = new DeleteCommand(params);

			await this.docClient.send(command);
		} catch (error) {
			console.error(`Error deleting user with id ${userId}:`, error);
			throw error;
		}
	}

	/**
	 * Update the activation status of a user
	 * @param userId User ID
	 * @param isActive New activation status
	 * @returns Promise with the result of the operation
	 */
	async updateUserActiveStatus(
		userId: string,
		isActive: boolean,
	): Promise<void> {
		try {
			const params: UpdateCommandInput = {
				TableName: this.tableName,
				Key: {
					id: userId,
				},
				UpdateExpression: "set isActive = :isActive, updatedAt = :updatedAt",
				ExpressionAttributeValues: {
					":isActive": isActive,
					":updatedAt": new Date().toISOString(),
				},
				ReturnValues: "NONE",
			};

			const command = new UpdateCommand(params);

			await this.docClient.send(command);
		} catch (error) {
			console.error(`Error updating active status for user ${userId}:`, error);
			throw error;
		}
	}

	/**
	 * Update a user's role
	 * @param userId User ID
	 * @param role New role
	 * @returns Promise with the result of the operation
	 */
	async updateUserRole(userId: string, role: UserRole): Promise<void> {
		try {
			const params: UpdateCommandInput = {
				TableName: this.tableName,
				Key: {
					id: userId,
				},
				UpdateExpression: "set #role = :role, updatedAt = :updatedAt",
				ExpressionAttributeNames: {
					"#role": "role", // Use ExpressionAttributeNames because 'role' can be a reserved word
				},
				ExpressionAttributeValues: {
					":role": role,
					":updatedAt": new Date().toISOString(),
				},
				ReturnValues: "NONE",
			};

			const command = new UpdateCommand(params);

			await this.docClient.send(command);
		} catch (error) {
			console.error(`Error updating role for user ${userId}:`, error);
			throw error;
		}
	}
}
