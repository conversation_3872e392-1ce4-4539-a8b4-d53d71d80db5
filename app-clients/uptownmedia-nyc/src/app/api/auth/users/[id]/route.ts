import { UserServices } from "../services/userServices";

export async function GET(
	_: Request,
	props: { params: Promise<{ id: string }> },
) {
	try {
		const params = await props.params;
		const userServices = new UserServices();

		const user = await userServices.getUserById(params.id);

		if (!user) {
			return Response.json({ error: "User not found" }, { status: 404 });
		}

		// Return safe user data (without password)
		const safeUserData = user.toSafeObject();

		return Response.json(safeUserData, { status: 200 });
	} catch (error) {
		console.error("Error fetching user:", error);
		return Response.json({ error: "Internal server error" }, { status: 500 });
	}
}

export async function PUT(
	_: Request,
	props: { params: Promise<{ id: string }> },
) {
	const params = await props.params;
	const userServices = new UserServices();

	return Response.json({ msg: "editor code added " });
}
