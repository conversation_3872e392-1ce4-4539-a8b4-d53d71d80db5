import { VerificationTokenServices } from "@skoolscout/verification-services";

import { UserServices } from "../users/services/userServices";

import { configDbEnv } from "@/utils/configDbEnv";
import { dDocClient } from "@/utils/dynamoclient";

const verificationTokenServices = new VerificationTokenServices(
	dDocClient,
	configDbEnv.authVerificationTokenTable,
);
const userServices = new UserServices();

export async function POST(request: Request) {
	try {
		const body = await request.json();
		const { verificationCode } = body;

		if (!verificationCode) {
			return Response.json(
				{ message: "verification code are required", success: false },
				{ status: 400 },
			);
		}

		if (verificationCode.length !== 6) {
			return Response.json(
				{ message: "Verification code must be 6 digits", success: false },
				{ status: 400 },
			);
		}

		const isValid =
			await verificationTokenServices.validateVerificationToken(
				verificationCode,
			);
		const token =
			await verificationTokenServices.getVerificationTokenByToken(
				verificationCode,
			);

		if (!token) {
			await userServices.addFailedAttempt(token.refId);

			return Response.json(
				{ msg: "Verification code not found", success: false },
				{ status: 400 },
			);
		}
		if (token.isExpired()) {
			await userServices.addFailedAttempt(token.refId);

			return Response.json(
				{ msg: "Verification code expired", success: false },
				{ status: 400 },
			);
		}

		if (!isValid) {
			await userServices.addFailedAttempt(token.refId);

			return Response.json(
				{ msg: "Verification code is invalid", success: false },
				{ status: 400 },
			);
		}

		await verificationTokenServices.markTokenAsUsed(token.token);
		await userServices.resetFailedAttempt(token.refId);

		return Response.json(
			{ message: "Verification code is valid", success: true },
			{ status: 200 },
		);
	} catch (error) {
		if (error instanceof Error) {
			return Response.json(
				{
					message: "Internal Server Error",
					error: error.message,
					success: false,
				},
				{ status: 500 },
			);
		}
	}
}
