export class GroupMembership {
	private tenant_id: string;
	private user_id: string;
	private requested_at: number;
	private approved: boolean;
	private approved_at?: number;
	private group_id: string;

	constructor(
		tenant_id: string,
		user_id: string,
		group_id: string,
		approved = false,
		approved_at?: number,
	) {
		// Generate UUIDv7 or use crypto.randomUUID() as fallback
		this.tenant_id = tenant_id;
		this.user_id = user_id;
		this.requested_at = Math.floor(Date.now() / 1000); // Unix timestamp in seconds
		this.approved = approved;
		this.approved_at = approved_at;
		this.group_id = group_id;
	}

	getTenantId(): string {
		return this.tenant_id;
	}

	setTenantId(value: string): void {
		this.tenant_id = value;
	}

	getUserId(): string {
		return this.user_id;
	}

	setUserId(value: string): void {
		this.user_id = value;
	}

	getRequestedAt(): number {
		return this.requested_at;
	}

	isApproved(): boolean {
		return this.approved;
	}

	setApproved(value: boolean): void {
		this.approved = value;
		if (value) {
			this.approved_at = Math.floor(Date.now() / 1000);
		}
	}

	getApprovedAt(): number | undefined {
		return this.approved_at;
	}

	setApprovedAt(value?: number): void {
		this.approved_at = value;
	}
	getGroupId(): string {
		return this.group_id;
	}
	setGroupId(value: string): void {
		this.group_id = value;
	}
}
