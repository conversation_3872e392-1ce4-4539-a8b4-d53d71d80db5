import {
	DeleteCommand,
	type DeleteCommandInput,
	PutCommand,
	type PutCommandInput,
	type ScanCommandInput,
	UpdateCommand,
	type UpdateCommandInput,
} from "@aws-sdk/lib-dynamodb";

import { BaseDynamoDBRepository } from "../../shared/dynamoDbRepository";
import { GroupMembership } from "../dao/groupMembership";

import GROUP_MEMBERSHIPS_DATA from "@/app/api/shared/mock-data/groupMemberships.json"; // Import mock data for group memberships
import { configDbEnv } from "@/utils/configDbEnv";

/**
 * Repository for interacting with the GroupMemberships table in DynamoDB
 */
export class GroupMembershipDynamoDBRepository extends BaseDynamoDBRepository {
	/**
	 * Constructor to set the tableName for GroupMembershipDynamoDBRepository
	 */
	constructor() {
		super(configDbEnv.authGroupMembershipTable); // Sets the tableName for this repository
	}

	/**
	 * Convert a DynamoDB object to an instance of GroupMembership
	 * @param item Object received from DynamoDB
	 * @returns Instance of GroupMembership
	 */
	// biome-ignore lint/suspicious/noExplicitAny: <explanation>
	private mapToGroupMembership(item: any): GroupMembership {
		const groupMembership = new GroupMembership(
			item.tenant_id,
			item.user_id,
			item.group_id,
			item.approved,
			item.approved_at,
		);

		if (item.requested_at) {
			Object.defineProperty(groupMembership, "requested_at", {
				value: item.requested_at,
				writable: false,
				configurable: true,
			});
		}

		return groupMembership;
	}

	/**
	 * Convert a GroupMembership instance to an object for saving in DynamoDB
	 * @param groupMembership Instance of GroupMembership
	 * @returns Object ready to be saved in DynamoDB
	 */
	private mapToDynamoDBItem(
		groupMembership: GroupMembership,
		// biome-ignore lint/suspicious/noExplicitAny: <explanation>
	): Record<string, any> {
		return {
			tenant_id: groupMembership.getTenantId(),
			user_id: groupMembership.getUserId(),
			group_id: groupMembership.getGroupId(),
			requested_at: groupMembership.getRequestedAt(),
			approved: groupMembership.isApproved(),
			approved_at: groupMembership.getApprovedAt(),
		};
	}

	/**
	 * Create or update a group membership in DynamoDB
	 * @param groupMembership GroupMembership instance to save
	 * @returns Promise with the result of the operation
	 */
	async saveGroupMembership(groupMembership: GroupMembership): Promise<void> {
		try {
			console.log("Saving group membership:", groupMembership);
			const item = this.mapToDynamoDBItem(groupMembership);

			const params: PutCommandInput = {
				TableName: this.tableName,
				Item: item,
			};

			const command = new PutCommand(params);

			await this.docClient.send(command);
		} catch (error) {
			console.error(
				`Error saving group membership with id ${groupMembership.getGroupId()}:`,
				error,
			);
			throw error;
		}
	}

	/**
	 * Get a group membership by its ID
	 * @param groupMembershipId Group Membership ID
	 * @returns Promise with the found group membership or null
	 */
	async getGroupMembershipById(
		groupId: string,
		userId: string,
	): Promise<GroupMembership | null> {
		// --- MOCK DATA LOGIC ---
		// Commented out DynamoDB logic until DB is enabled
		try {
			const filterExpression = "group_id = :group_id AND user_id = :user_id";

			const expressionAttributeValues = {
				":group_id": groupId,
				":user_id": userId,
			};

			const params: ScanCommandInput = {
				TableName: this.tableName,
				FilterExpression: filterExpression,
				ExpressionAttributeValues: expressionAttributeValues,
			};

			const items = await this.fetchAllItems(params);

			console.log("Items fetched:", items);

			return this.mapToGroupMembership(items[0]);
		} catch (error) {
			console.error(
				`Error fetching group membership with id ${userId}:`,
				error,
			);
			throw error;
		}
	}

	/**
	 * Get all group memberships
	 * @returns Promise with a list of all group memberships
	 */
	async getAllGroupMemberships(): Promise<GroupMembership[]> {
		// --- MOCK DATA LOGIC ---
		// Commented out DynamoDB logic until DB is enabled
		try {
			const items = await this.getAllItems();

			return items.map((item) => this.mapToGroupMembership(item));
		} catch (error) {
			console.error("Error fetching all group memberships:", error);
			throw error;
		}
	}
	async getGroupMembershipsByGroupId(groupId: string) {
		try {
			const filterExpression = "group_id = :group_id";

			console.log("Group ID:", groupId);

			const expressionAttributeValues = {
				":group_id": groupId,
			};

			const params: ScanCommandInput = {
				TableName: this.tableName,
				FilterExpression: filterExpression,
				ExpressionAttributeValues: expressionAttributeValues,
			};

			const items = await this.fetchAllItems(params);

			console.log("Items fetched:", items);

			return items.map((item) => this.mapToGroupMembership(item));
		} catch (error) {
			console.error("Error fetching all group memberships:", error);
			throw error;
		}
	}

	/**
	 * Get all group memberships for a specific tenant
	 * @param tenantId Tenant ID to filter by
	 * @returns Promise with a list of group memberships for the specified tenant
	 */
	async getGroupMembershipsByTenantId(
		tenantId: string,
	): Promise<GroupMembership[]> {
		// --- MOCK DATA LOGIC ---
		// Commented out DynamoDB logic until DB is enabled
		// try {
		//   const items = await this.scanWithFilter("tenant_id = :tenant_id", {
		//     ":tenant_id": tenantId,
		//   });
		//   return items.map((item) => this.mapToGroupMembership(item));
		// } catch (error) {
		//   console.error(`Error fetching group memberships for tenant ${tenantId}:`, error);
		//   throw error;
		// }
		return GROUP_MEMBERSHIPS_DATA.filter(
			(item) => item.tenant_id === tenantId,
		).map((item) => this.mapToGroupMembership(item));
	}

	/**
	 * Get all group memberships for a specific user
	 * @param userId User ID to filter by
	 * @returns Promise with a list of group memberships for the specified user
	 */
	async getGroupMembershipsByUserId(
		userId: string,
	): Promise<GroupMembership[]> {
		// --- MOCK DATA LOGIC ---
		// Commented out DynamoDB logic until DB is enabled
		// try {
		//   const items = await this.scanWithFilter("user_id = :user_id", {
		//     ":user_id": userId,
		//   });
		//   return items.map((item) => this.mapToGroupMembership(item));
		// } catch (error) {
		//   console.error(`Error fetching group memberships for user ${userId}:`, error);
		//   throw error;
		// }
		return GROUP_MEMBERSHIPS_DATA.filter((item) => item.user_id === userId).map(
			(item) => this.mapToGroupMembership(item),
		);
	}

	/**
	 * Delete a group membership by its ID
	 * @param groupMembershipId ID of the group membership to delete
	 * @returns Promise with the result of the operation
	 */
	async deleteGroupMembership(groupMembershipId: string): Promise<void> {
		try {
			const params: DeleteCommandInput = {
				TableName: this.tableName,
				Key: {
					id: groupMembershipId,
				},
			};

			const command = new DeleteCommand(params);

			await this.docClient.send(command);
		} catch (error) {
			console.error(
				`Error deleting group membership with id ${groupMembershipId}:`,
				error,
			);
			throw error;
		}
	}

	/**
	 * Update group membership information
	 * @param groupMembershipId ID of the group membership
	 * @param groupMembershipData Group membership data to update
	 * @returns Promise with the result of the operation
	 */
	async updateGroupMembershipInfo(
		groupMembershipId: string,
		groupMembershipData: {
			tenant_id?: string;
			user_id?: string;
			approved?: boolean;
			approved_at?: number;
		},
	): Promise<void> {
		try {
			// Dynamically build the update expression based on provided fields
			let updateExpression = "set";
			// biome-ignore lint/suspicious/noExplicitAny: <explanation>
			const expressionAttributeValues: Record<string, any> = {};

			// Add fields to the update expression if present
			if (groupMembershipData.tenant_id) {
				updateExpression += " tenant_id = :tenant_id,";
				expressionAttributeValues[":tenant_id"] = groupMembershipData.tenant_id;
			}

			if (groupMembershipData.user_id) {
				updateExpression += " user_id = :user_id,";
				expressionAttributeValues[":user_id"] = groupMembershipData.user_id;
			}

			if (groupMembershipData.approved !== undefined) {
				updateExpression += " approved = :approved,";
				expressionAttributeValues[":approved"] = groupMembershipData.approved;
			}

			if (groupMembershipData.approved_at !== undefined) {
				updateExpression += " approved_at = :approved_at,";
				expressionAttributeValues[":approved_at"] =
					groupMembershipData.approved_at;
			}

			// Remove trailing comma if present
			updateExpression = updateExpression.endsWith(",")
				? updateExpression.slice(0, -1)
				: updateExpression;

			// If there's nothing to update, return early
			if (Object.keys(expressionAttributeValues).length === 0) {
				return;
			}

			const params: UpdateCommandInput = {
				TableName: this.tableName,
				Key: {
					id: groupMembershipId,
				},
				UpdateExpression: updateExpression,
				ExpressionAttributeValues: expressionAttributeValues,
				ReturnValues: "NONE",
			};

			const command = new UpdateCommand(params);

			await this.docClient.send(command);
		} catch (error) {
			console.error(
				`Error updating info for group membership ${groupMembershipId}:`,
				error,
			);
			throw error;
		}
	}
}
