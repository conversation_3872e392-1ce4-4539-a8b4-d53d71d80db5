import { GroupMembershipServices } from "@/app/api/group-memberships/services/groupMembershipsServices";

const groupMembershipServices = new GroupMembershipServices();

export async function POST(
	request: Request,
	props: { params: Promise<{ groupId: string; userId: string }> },
) {
	const params = await props.params;

	await groupMembershipServices.approveGroupMembershipById(
		params.groupId,
		params.userId,
	);

	return Response.json({ msg: "member approved" }, { status: 200 });
}
