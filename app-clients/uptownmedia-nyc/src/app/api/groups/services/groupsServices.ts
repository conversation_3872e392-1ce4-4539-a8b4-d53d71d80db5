import type { GroupRequest } from "../dto/groupRequest";

import { ConflictError, NotFoundError } from "../../shared/errors";
import { Group } from "../dao/group";
import { GroupDynamoDBRepository } from "../repository/groupRepository";

export class GroupServices {
	private repository: GroupDynamoDBRepository;

	constructor() {
		this.repository = new GroupDynamoDBRepository();
	}

	public async createGroup(request: GroupRequest) {
		const {
			editor_id,
			group_name,
			group_type,
			shared_passcode,
			subscription_id,
			subscription_plan,
			subscription_status,
			expires_at,
		} = request;
		const tenant_id = "1";

		// let code = `${getRandomInt(0, 10000)}-${randomLetters(4)}-${randomLetters(3)}`;
		// let exist = await this.repository.getGroupBySharedPasscode(code);
		//
		// do {
		//   code = `${getRandomInt(0, 10000)}-${randomLetters(4)}-${randomLetters(3)}`;
		//   exist = await this.repository.getGroupBySharedPasscode(code);
		// } while (exist);
		//
		const codeExist =
			await this.repository.getGroupBySharedPasscode(shared_passcode);

		if (codeExist) {
			throw new ConflictError(
				`Group with shared passcode ${shared_passcode} already exists`,
			);
		}

		console.log("Creating group with request:", request);
		const newGroup = new Group(
			tenant_id,
			editor_id,
			group_name,
			shared_passcode,
			group_type,
			subscription_id,
			subscription_plan,
			subscription_status,
			expires_at,
		);

		try {
			await this.repository.saveGroup(newGroup);
			console.log("Group created successfully:", newGroup);
		} catch (error) {
			console.error("Error saving group:", error);
		}
	}

	public async getGroup() {
		return await this.repository.getAllGroups();
	}

	public async getGroupById(id: string) {
		const group = await this.repository.getGroupById(id);

		if (!group) {
			throw new NotFoundError(`Group with ID ${id} not found`);
		}

		return group;
	}
	public async getGroupByEditorId(editorId: string) {
		const group = await this.repository.getGroupByEditorId(editorId);

		if (!group) {
			throw new NotFoundError(`Group with editor ID ${editorId} not found`);
		}

		return group;
	}
	public async getGroupByEditorIdAndGroupType(
		editorId: string,
		groupType: string,
	) {
		const group = await this.repository.getGroupByEditorIdAndGroupType(
			editorId,
			groupType,
		);

		if (!group) {
			throw new NotFoundError(
				`Group with editor ID ${editorId} and type ${groupType} not found`,
			);
		}

		return group;
	}
	public async getGroupBySharePassCode(sharedPasscode: string) {
		const group =
			await this.repository.getGroupBySharedPasscode(sharedPasscode);

		if (!group) {
			throw new NotFoundError(
				`Group with shared passcode ${sharedPasscode} not found`,
			);
		}

		return group;
	}
}

function getRandomInt(min: number, max: number): number {
	const range = max - min;
	const randomBuffer = new Uint32Array(1);

	crypto.getRandomValues(randomBuffer);

	return min + (randomBuffer[0] % range);
}
function randomLetters(length: number): string {
	const letters = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";

	return Array.from({ length }, () =>
		letters.charAt(getRandomInt(0, letters.length)),
	).join("");
}
