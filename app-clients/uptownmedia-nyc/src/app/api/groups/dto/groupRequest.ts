import { z } from "zod";

export const GroupRequestSchema = z.object({
	editor_id: z.string().min(1, "editor_id is required"),
	group_name: z.string().min(1, "group_name is required"),
	group_type: z.string().min(1, "group_type is required"),
	shared_passcode: z.string(),
	subscription_id: z.string().optional(),
	subscription_plan: z.string().optional(),
	subscription_status: z.string().optional(),
	expires_at: z.number().optional(),
});
// .merge(BaseSchema);

export type GroupRequest = z.infer<typeof GroupRequestSchema>;
