import { uuidv7 } from "uuidv7";

export class Group {
	private groupId: string;
	private tenant_id: string;
	private editor_id: string;
	private group_name: string;
	private shared_passcode: string;
	private group_type: string;
	private subscription_id?: string;
	private subscription_plan?: string;
	private subscription_status?: string;
	private created_at: number;
	private expires_at?: number;

	constructor(
		tenant_id: string,
		editor_id: string,
		group_name: string,
		shared_passcode: string,
		group_type: string,
		subscription_id?: string,
		subscription_plan?: string,
		subscription_status?: string,
		expires_at?: number,
	) {
		// Generate UUIDv7 or use crypto.randomUUID() as fallback
		this.groupId = uuidv7();
		this.tenant_id = tenant_id;
		this.editor_id = editor_id;
		this.group_name = group_name;
		this.shared_passcode = shared_passcode;
		this.group_type = group_type;
		this.subscription_id = subscription_id;
		this.subscription_plan = subscription_plan;
		this.subscription_status = subscription_status;
		this.created_at = Math.floor(Date.now() / 1000); // Unix timestamp in seconds
		this.expires_at = expires_at;
	}

	getGroupId(): string {
		return this.groupId;
	}
	setGroupId(value: string): void {
		this.groupId = value;
	}

	getTenantId(): string {
		return this.tenant_id;
	}

	setTenantId(value: string): void {
		this.tenant_id = value;
	}

	getEditorId(): string {
		return this.editor_id;
	}

	setEditorId(value: string): void {
		this.editor_id = value;
	}

	getGroupName(): string {
		return this.group_name;
	}

	setGroupName(value: string): void {
		this.group_name = value;
	}

	getSharedPasscode(): string {
		return this.shared_passcode;
	}

	setSharedPasscode(value: string): void {
		this.shared_passcode = value;
	}

	getGroupType(): string {
		return this.group_type;
	}

	setGroupType(value: string): void {
		this.group_type = value;
	}

	getSubscriptionId(): string | undefined {
		return this.subscription_id;
	}

	setSubscriptionId(value?: string): void {
		this.subscription_id = value;
	}

	getSubscriptionPlan(): string | undefined {
		return this.subscription_plan;
	}

	setSubscriptionPlan(value?: string): void {
		this.subscription_plan = value;
	}

	getSubscriptionStatus(): string | undefined {
		return this.subscription_status;
	}

	setSubscriptionStatus(value?: string): void {
		this.subscription_status = value;
	}

	getCreatedAt(): number {
		return this.created_at;
	}

	getExpiresAt(): number | undefined {
		return this.expires_at;
	}

	setExpiresAt(value?: number): void {
		this.expires_at = value;
	}
}
