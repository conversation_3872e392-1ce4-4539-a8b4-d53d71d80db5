import { z } from "zod";

export const ArticleRequestSchema = z.object({
	title: z.string().min(1, "Title must be at least 1 character"),
	description: z.string().min(1, "Description must be at least 1 character"),
	author: z.string().min(1, "Author must be at least 1 character"),
	topic: z.string().min(1, "Topic must be at least 1 character"),
	published: z
		.string()
		.datetime("Published must be a valid ISO datetime string"),
	image: z.string().url("Image must be a valid URL"),
	featured: z.boolean(),
	approved: z.string().datetime("Approved must be a valid ISO datetime string"),
	approvedBy: z.string().min(1, "ApprovedBy must be at least 1 character"),
	placement: z.number().int().nonnegative(),
});

export type ArticleRequest = z.infer<typeof ArticleRequestSchema>;
