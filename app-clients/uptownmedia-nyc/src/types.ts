import type { ReactNode } from "react";
import type { UserRole } from "./app/api/auth/users/dao/user";
// export type UserRole = "Editor" | "Author" | "Guest";

export type User = {
	id?: string;
	name?: string | null;
	email?: string | null;
	image?: string | null;
	roles?: UserRole[];
	isEditor?: boolean;
	isAdmin?: boolean;
	isAuthor?: boolean;
	isGuess?: boolean;
	tokenLink?: string;
};

export type UserPreferences = {
	theme: string;
	sidebar: {
		compact: boolean;
	};
};
