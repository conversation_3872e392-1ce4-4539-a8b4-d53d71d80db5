import type { FilterFunction } from "@/libs/middleware/types";

import { adminAuthFilter } from "./adminAuthFilter";
import { apiLoggingFilter } from "./apiLoggingFilter";
import { ApiAuthFilter } from "./apiauthFilter";
import { authTokenLinkFilter } from "./authTokenLinkFilter";
import { authorAuthFilter } from "./authorAuthFilter";
import { editorAuthFilter } from "./editorAuthFilter";
import { jwtProtectedUserAuthFilter } from "./jwtProtectedUserAuthFilter";
import { nextAuthFilter } from "./nextAuthFilter";
import { superUserAuthFilter } from "./superUserAuthFilter";

// Map paths to middleware functions
export const filterMap: Record<string, { [method: string]: FilterFunction[] }> =
	{
		"/api/auth/users/admin": {
			GET: [apiLoggingFilter, superUserAuthFilter],
			POST: [apiLogging<PERSON>ilter, superUserAuthFilter],
			PUT: [apiLogging<PERSON>ilter, superUserAuthFilter],
			DELETE: [apiLoggingFilter, superUserAuthFilter],
		},
		"/api/auth/tenant": {
			GET: [apiLoggingFilter, superUserAuthFilter],
			POST: [apiLoggingFilter, superUserAuthFilter],
		},
		"/api/auth/users/editor": {
			GET: [apiLoggingFilter, ApiAuthFilter, adminAuthFilter],
			POST: [apiLoggingFilter, ApiAuthFilter, adminAuthFilter],
		},

		"/api/auth/users/author": {
			GET: [apiLoggingFilter, ApiAuthFilter, editorAuthFilter],
			POST: [apiLoggingFilter, ApiAuthFilter, editorAuthFilter],
		},
		"/api/auth/users": {
			GET: [apiLoggingFilter, ApiAuthFilter, adminAuthFilter],
			POST: [apiLoggingFilter, ApiAuthFilter, adminAuthFilter],
			PUT: [apiLoggingFilter, ApiAuthFilter, adminAuthFilter],
			PATCH: [apiLoggingFilter, ApiAuthFilter, adminAuthFilter],
			DELETE: [apiLoggingFilter, ApiAuthFilter, adminAuthFilter],
		},

		"/api/articles": {
			POST: [apiLoggingFilter, authorAuthFilter],
		},
		"/api/comments": {
			POST: [apiLoggingFilter, ApiAuthFilter],
		},
		"/api/auth/access-logs": {
			GET: [apiLoggingFilter, ApiAuthFilter, adminAuthFilter],
		},
		"/api/auth/verification-code": {
			GET: [apiLoggingFilter, ApiAuthFilter],
			POST: [apiLoggingFilter, ApiAuthFilter],
			PUT: [apiLoggingFilter, ApiAuthFilter],
			PATCH: [apiLoggingFilter, ApiAuthFilter],
			DELETE: [apiLoggingFilter, ApiAuthFilter],
		},
		"/api/auth/signing-token-link": {
			GET: [apiLoggingFilter, ApiAuthFilter],
			POST: [apiLoggingFilter, ApiAuthFilter],
		},
		"/api/auth/roles": {
			GET: [apiLoggingFilter, ApiAuthFilter],
			POST: [apiLoggingFilter, ApiAuthFilter],
		},
		"/portal": {
			GET: [nextAuthFilter],
		},
		"/auth/sign-in": {
			GET: [authTokenLinkFilter],
		},
		"/": {
			GET: [apiLoggingFilter],
		},

		"/ops": {
			GET: [],
		},
		DEFAULT: {
			GET: [jwtProtectedUserAuthFilter, apiLoggingFilter],
			POST: [jwtProtectedUserAuthFilter, apiLoggingFilter],
			PUT: [jwtProtectedUserAuthFilter, apiLoggingFilter],
		},
	};
