name: CI-localStack

on:
  pull_request:
    paths:
      - "app-clients/**"
    branches: [main, develop, stage]

jobs:
  build:
    if: ${{ !(github.head_ref == 'develop' && github.base_ref == 'stage') }}
    runs-on: jefelabs-runner-ci
    permissions:
      contents: write
      pull-requests: write
      checks: write
    steps:
      - uses: actions/checkout@v3
        with:
          fetch-depth: 0
          ref: ${{ github.head_ref }}
          token: ${{ secrets.AUTH_TOKEN }}
          submodules: recursive
      - name: create pr on develop
        run: |
          ./.scripts/hotfix-merger.sh
        env:
          AUTH_GITHUB_TOKEN: ${{ secrets.AUTH_TOKEN }}
          GITHUB_PR_BRANCH: ${{ github.base_ref }}
          GITHUB_TOKEN: ${{ secrets.AUTH_TOKEN }}

      - uses: actions/setup-node@v4
        with:
          node-version: "22.13.0"
      - name: Setup Gradle
        uses: gradle/actions/setup-gradle@v3
        with:
          gradle-version: 8.6
      - name: Setup Java JDK
        uses: actions/setup-java@v3.10.0
        with:
          distribution: "corretto"
          java-version: "21"
      - name: Configure npm for GitHub Packages
        run: |
          echo "//npm.pkg.github.com/:_authToken=${{ secrets.AUTH_TOKEN }}" > ~/.npmrc
          echo "@skoolscout:registry=https://npm.pkg.github.com" >> ~/.npmrc

      - name: Install private package globally
        run: npm install -g @skoolscout/gen-dotenv@0.4.2

      - name: Get the latest commit message
        id: get_commit_message
        run: echo "COMMIT_MESSAGE=$(git log -1 --pretty=format:%s)" >> $GITHUB_ENV

      - name: Install Dependencies
        if: env.COMMIT_MESSAGE != 'Update changeset file'
        run: |

          npm install

      - name: make run local
        if: env.COMMIT_MESSAGE != 'Update changeset file'
        shell: bash
        run: |
          echo $GITHUB_PR_BRANCH
          ./gradlew deployTenants
        env:
          # LOCALSTACK_AUTH_TOKEN: ${{ secrets.LOCALSTACK_AUTH_TOKEN }}
          ENVIRONMENT: "local"
          AUTH_GITHUB_TOKEN: ${{ secrets.AUTH_TOKEN }}
          # DOCKER_REGISTRY: ${{ secrets.DOCKER_REGISTRY }}
          # DOCKER_USER: ${{ secrets.DOCKER_USER }}
          # DOCKER_PASSWORD: ${{ secrets.DOCKER_PASSWORD }}
          # GIT_REPO_NAME: "skoolscout/skoolscout-com-tenants:latest"
          # DOCKER_IMAGE: "skoolscout-com-tenants"
          NEXT_PUBLIC_BACKEND_API: "http:localhost:8080"
          AUTH_SECRET: tXDRneaJthv85iAiBc1sZi5hry/Ajy5aGkQATYDeCZ0=
          # QODANA_TOKEN: ${{ secrets.QODANA_TOKEN }}
          # FONTAWESOME_PACKAGE_TOKEN: ${{ secrets.FONTAWESOME_PACKAGE_TOKEN }}
          AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
          AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          GITHUB_PR_BRANCH: ${{ github.base_ref }}

      - name: Commit changes
        if: env.COMMIT_MESSAGE != 'Update changeset file' && github.base_ref != 'stage'
        uses: EndBug/add-and-commit@v9
        with:
          author_name: "github-actions"
          author_email: "<EMAIL>"
          message: "Update changeset file"

      - name: Send notification to Discord For approve PR
        if: github.event_name == 'pull_request' && env.COMMIT_MESSAGE != 'Update changeset file'
        uses: appleboy/discord-action@master
        with:
          webhook_id: ${{ secrets.DISCORD_WEBHOOK_ID }}
          webhook_token: ${{ secrets.DISCORD_WEBHOOK_TOKEN }}
          message: |
            Tests completed on create Pull Request: ${{ github.event.pull_request.title }}. Please check and approve
            Project: ${{ github.repository }}
            PR: ${{ github.head_ref }} --> ${{ github.base_ref }}
            PR Actor: ${{ github.actor }}
            PR Link: ${{ github.event.pull_request.html_url }}
